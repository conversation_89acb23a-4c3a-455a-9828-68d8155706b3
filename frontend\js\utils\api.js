// API Utility Functions
class APIClient {
    constructor() {
        this.baseURL = CONFIG.API.BASE_URL;
        this.timeout = CONFIG.API.TIMEOUT;
        this.retryAttempts = CONFIG.API.RETRY_ATTEMPTS;
    }

    // Get authentication token
    getAuthToken() {
        return localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
    }

    // Set default headers
    getHeaders(includeAuth = true) {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };

        if (includeAuth) {
            const token = this.getAuthToken();
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
        }

        return headers;
    }

    // Handle API response
    async handleResponse(response) {
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            const error = new Error(errorData.message || CONFIG.ERRORS.SERVER_ERROR);
            error.status = response.status;
            error.data = errorData;
            throw error;
        }

        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        }

        return await response.text();
    }

    // Make HTTP request with retry logic
    async makeRequest(url, options = {}, attempt = 1) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            const response = await fetch(`${this.baseURL}${url}`, {
                ...options,
                headers: {
                    ...this.getHeaders(),
                    ...options.headers
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);
            return await this.handleResponse(response);
        } catch (error) {
            clearTimeout(timeoutId);

            // Handle abort (timeout)
            if (error.name === 'AbortError') {
                throw new Error(CONFIG.ERRORS.TIMEOUT);
            }

            // Retry on network errors
            if (attempt < this.retryAttempts && error.message.includes('fetch')) {
                await this.delay(1000 * attempt);
                return this.makeRequest(url, options, attempt + 1);
            }

            // Handle authentication errors
            if (error.status === 401) {
                this.handleAuthError();
                throw new Error(CONFIG.ERRORS.UNAUTHORIZED);
            }

            throw error;
        }
    }

    // Delay function for retry logic
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Handle authentication errors
    handleAuthError() {
        localStorage.removeItem(CONFIG.AUTH.TOKEN_KEY);
        localStorage.removeItem(CONFIG.AUTH.USER_KEY);
        
        // Redirect to login if not already there
        if (!window.location.pathname.includes('login')) {
            window.location.href = '/pages/login.html';
        }
    }

    // GET request
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        return this.makeRequest(fullUrl, {
            method: 'GET'
        });
    }

    // POST request
    async post(url, data = {}) {
        return this.makeRequest(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT request
    async put(url, data = {}) {
        return this.makeRequest(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // PATCH request
    async patch(url, data = {}) {
        return this.makeRequest(url, {
            method: 'PATCH',
            body: JSON.stringify(data)
        });
    }

    // DELETE request
    async delete(url) {
        return this.makeRequest(url, {
            method: 'DELETE'
        });
    }

    // Upload file
    async uploadFile(url, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key]);
        });

        return this.makeRequest(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.getAuthToken()}`
                // Don't set Content-Type for FormData
            },
            body: formData
        });
    }
}

// Create global API instance
const api = new APIClient();

// Specific API endpoints
const API = {
    // Authentication
    auth: {
        login: (credentials) => api.post('/auth/login', credentials),
        register: (userData) => api.post('/auth/register', userData),
        logout: () => api.post('/auth/logout'),
        refreshToken: () => api.post('/auth/refresh'),
        forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
        resetPassword: (token, password) => api.post('/auth/reset-password', { token, password }),
        verifyEmail: (token) => api.post('/auth/verify-email', { token })
    },

    // User Profile
    user: {
        getProfile: () => api.get('/user/profile'),
        updateProfile: (data) => api.put('/user/profile', data),
        changePassword: (data) => api.post('/user/change-password', data),
        uploadAvatar: (file) => api.uploadFile('/user/avatar', file),
        deleteAccount: () => api.delete('/user/account')
    },

    // Nutrition
    nutrition: {
        getPlans: (params) => api.get('/nutrition/plans', params),
        getPlan: (id) => api.get(`/nutrition/plans/${id}`),
        createPlan: (data) => api.post('/nutrition/plans', data),
        updatePlan: (id, data) => api.put(`/nutrition/plans/${id}`, data),
        deletePlan: (id) => api.delete(`/nutrition/plans/${id}`)
    },

    // Meal Plans
    mealPlans: {
        getAll: (params) => api.get('/meal-plans', params),
        getByDay: (day, trimester, cuisine) => api.get('/meal-plans/day', { day, trimester, cuisine }),
        create: (data) => api.post('/meal-plans', data),
        update: (id, data) => api.put(`/meal-plans/${id}`, data),
        delete: (id) => api.delete(`/meal-plans/${id}`)
    },

    // Weight Tracker
    weight: {
        getEntries: (params) => api.get('/weight/entries', params),
        addEntry: (data) => api.post('/weight/entries', data),
        updateEntry: (id, data) => api.put(`/weight/entries/${id}`, data),
        deleteEntry: (id) => api.delete(`/weight/entries/${id}`),
        getStats: () => api.get('/weight/stats')
    },

    // Exercise
    exercise: {
        getGuides: (params) => api.get('/exercise/guides', params),
        getGuide: (id) => api.get(`/exercise/guides/${id}`),
        createGuide: (data) => api.post('/exercise/guides', data),
        updateGuide: (id, data) => api.put(`/exercise/guides/${id}`, data),
        deleteGuide: (id) => api.delete(`/exercise/guides/${id}`)
    },

    // Baby Care
    babyCare: {
        getContent: (params) => api.get('/baby-care/content', params),
        getActivities: (params) => api.get('/baby-care/activities', params),
        getNutrition: (params) => api.get('/baby-care/nutrition', params),
        getSleepPatterns: (params) => api.get('/baby-care/sleep-patterns', params)
    },

    // Appointments
    appointments: {
        getAll: (params) => api.get('/appointments', params),
        getById: (id) => api.get(`/appointments/${id}`),
        create: (data) => api.post('/appointments', data),
        update: (id, data) => api.put(`/appointments/${id}`, data),
        cancel: (id) => api.delete(`/appointments/${id}`),
        getAvailableSlots: (date, doctorId) => api.get('/appointments/available-slots', { date, doctorId })
    },

    // Vaccinations
    vaccinations: {
        getSchedule: (params) => api.get('/vaccinations/schedule', params),
        getRecords: (params) => api.get('/vaccinations/records', params),
        addRecord: (data) => api.post('/vaccinations/records', data),
        updateRecord: (id, data) => api.put(`/vaccinations/records/${id}`, data)
    },

    // Schemes
    schemes: {
        getAll: (params) => api.get('/schemes', params),
        getById: (id) => api.get(`/schemes/${id}`),
        apply: (id, data) => api.post(`/schemes/${id}/apply`, data)
    },

    // Chatbot
    chatbot: {
        sendMessage: (message, context) => api.post('/chatbot/message', { message, context }),
        getHistory: (params) => api.get('/chatbot/history', params),
        clearHistory: () => api.delete('/chatbot/history')
    },

    // Admin
    admin: {
        getDashboard: () => api.get('/admin/dashboard'),
        getUsers: (params) => api.get('/admin/users', params),
        updateUser: (id, data) => api.put(`/admin/users/${id}`, data),
        deleteUser: (id) => api.delete(`/admin/users/${id}`),
        getAnalytics: (params) => api.get('/admin/analytics', params)
    }
};

// Export API object
window.API = API;
