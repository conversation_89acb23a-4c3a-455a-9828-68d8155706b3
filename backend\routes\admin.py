"""
Admin routes for administrative functions and content management.
"""

import logging
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

from models.database import get_db
from utils.helpers import handle_error, convert_rows_to_dicts, require_admin

logger = logging.getLogger(__name__)
admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/dashboard', methods=['GET'])
@jwt_required()
@require_admin
def get_dashboard():
    """Get admin dashboard statistics."""
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get user statistics
            cursor.execute("SELECT COUNT(*) as total_users FROM users WHERE is_active = 1")
            total_users = cursor.fetchone()['total_users']
            
            cursor.execute("SELECT COUNT(*) as new_users FROM users WHERE date(created_at) = date('now')")
            new_users_today = cursor.fetchone()['new_users']
            
            # Get content statistics
            cursor.execute("SELECT COUNT(*) as total_plans FROM nutrition_plans WHERE is_active = 1")
            total_nutrition_plans = cursor.fetchone()['total_plans']
            
            cursor.execute("SELECT COUNT(*) as total_exercises FROM exercise_guides WHERE is_active = 1")
            total_exercises = cursor.fetchone()['total_exercises']
            
            cursor.execute("SELECT COUNT(*) as total_appointments FROM appointments WHERE status = 'scheduled'")
            scheduled_appointments = cursor.fetchone()['total_appointments']
            
            # Get recent activity
            cursor.execute("""
                SELECT u.first_name, u.last_name, u.email, u.created_at
                FROM users u
                ORDER BY u.created_at DESC
                LIMIT 10
            """)
            recent_users = convert_rows_to_dicts(cursor.fetchall())
            
            dashboard_data = {
                'statistics': {
                    'total_users': total_users,
                    'new_users_today': new_users_today,
                    'total_nutrition_plans': total_nutrition_plans,
                    'total_exercises': total_exercises,
                    'scheduled_appointments': scheduled_appointments
                },
                'recent_users': recent_users
            }
            
            return jsonify({'dashboard': dashboard_data}), 200
            
    except Exception as e:
        logger.error(f"Get admin dashboard error: {str(e)}")
        return handle_error(e)

@admin_bp.route('/users', methods=['GET'])
@jwt_required()
@require_admin
def get_users():
    """Get all users (admin only)."""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT id, email, first_name, last_name, phone, 
                       is_admin, is_active, created_at
                FROM users
            """
            params = []
            
            if search:
                query += " WHERE (first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)"
                search_term = f'%{search}%'
                params.extend([search_term, search_term, search_term])
            
            query += " ORDER BY created_at DESC"
            
            cursor.execute(query, params)
            users = convert_rows_to_dicts(cursor.fetchall())
            
            # Simple pagination
            start = (page - 1) * per_page
            end = start + per_page
            paginated_users = users[start:end]
            
            return jsonify({
                'users': paginated_users,
                'total': len(users),
                'page': page,
                'per_page': per_page,
                'pages': (len(users) + per_page - 1) // per_page
            }), 200
            
    except Exception as e:
        logger.error(f"Get users error: {str(e)}")
        return handle_error(e)
