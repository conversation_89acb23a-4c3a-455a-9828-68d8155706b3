/**
 * Chatbot component for AI-powered pregnancy and baby care assistance
 * Integrates with Gemini AI service for intelligent responses
 */

class ChatbotComponent {
    constructor() {
        this.container = document.getElementById('chatbot-container');
        this.toggle = document.getElementById('chatbot-toggle');
        this.closeBtn = document.getElementById('chatbot-close');
        this.messagesContainer = document.getElementById('chatbot-messages');
        this.input = document.getElementById('chatbot-input');
        this.sendBtn = document.getElementById('chatbot-send');
        
        this.isOpen = false;
        this.sessionId = this.generateSessionId();
        this.isTyping = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadConversationHistory();
    }

    setupEventListeners() {
        // Toggle chatbot
        if (this.toggle) {
            this.toggle.addEventListener('click', () => {
                this.toggleChatbot();
            });
        }

        // Close chatbot
        if (this.closeBtn) {
            this.closeBtn.addEventListener('click', () => {
                this.closeChatbot();
            });
        }

        // Send message
        if (this.sendBtn) {
            this.sendBtn.addEventListener('click', () => {
                this.sendMessage();
            });
        }

        // Send message on Enter key
        if (this.input) {
            this.input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            // Auto-resize input
            this.input.addEventListener('input', () => {
                this.autoResizeInput();
            });
        }

        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeChatbot();
            }
        });

        // Click outside to close
        document.addEventListener('click', (e) => {
            if (this.isOpen && !this.container.contains(e.target) && !this.toggle.contains(e.target)) {
                this.closeChatbot();
            }
        });
    }

    toggleChatbot() {
        if (this.isOpen) {
            this.closeChatbot();
        } else {
            this.openChatbot();
        }
    }

    openChatbot() {
        if (!this.container) return;
        
        this.container.classList.add('active');
        this.isOpen = true;
        
        // Focus input
        if (this.input) {
            setTimeout(() => {
                this.input.focus();
            }, 300);
        }

        // Mark as opened for analytics
        this.trackEvent('chatbot_opened');
    }

    closeChatbot() {
        if (!this.container) return;
        
        this.container.classList.remove('active');
        this.isOpen = false;
        
        // Track close event
        this.trackEvent('chatbot_closed');
    }

    async sendMessage() {
        const message = this.input?.value.trim();
        if (!message || this.isTyping) return;

        // Clear input
        this.input.value = '';
        this.autoResizeInput();

        // Add user message to chat
        this.addMessage(message, 'user');

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Send message to API
            const response = await API.chatbot.sendMessage({
                message: message,
                session_id: this.sessionId
            });

            // Remove typing indicator
            this.hideTypingIndicator();

            // Add bot response
            this.addMessage(response.response, 'bot');

            // Handle special responses
            if (response.sentiment === 'urgent') {
                this.showUrgentAlert();
            }

            // Track message sent
            this.trackEvent('message_sent', { message_length: message.length });

        } catch (error) {
            console.error('Chatbot error:', error);
            
            // Remove typing indicator
            this.hideTypingIndicator();
            
            // Show error message
            this.addMessage(
                "I'm sorry, I'm having trouble responding right now. Please try again later or contact your healthcare provider for urgent concerns.",
                'bot',
                'error'
            );
        }
    }

    addMessage(content, sender, type = 'normal') {
        if (!this.messagesContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        if (type === 'error') {
            messageDiv.classList.add('error-message');
        }

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = content;

        // Add timestamp
        const timestamp = document.createElement('div');
        timestamp.className = 'message-timestamp';
        timestamp.textContent = new Date().toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
        });

        messageDiv.appendChild(contentDiv);
        messageDiv.appendChild(timestamp);

        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();

        // Animate message appearance
        setTimeout(() => {
            messageDiv.classList.add('animate-in');
        }, 50);
    }

    showTypingIndicator() {
        if (this.isTyping) return;
        
        this.isTyping = true;
        
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot-message typing-indicator';
        typingDiv.id = 'typing-indicator';
        
        typingDiv.innerHTML = `
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;

        this.messagesContainer.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
        this.isTyping = false;
    }

    showUrgentAlert() {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'urgent-alert';
        alertDiv.innerHTML = `
            <div class="urgent-alert-content">
                <i class="fas fa-exclamation-triangle"></i>
                <span>If this is a medical emergency, please call emergency services immediately.</span>
            </div>
        `;

        this.messagesContainer.appendChild(alertDiv);
        this.scrollToBottom();

        // Auto-remove after 10 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 10000);
    }

    scrollToBottom() {
        if (this.messagesContainer) {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }
    }

    autoResizeInput() {
        if (!this.input) return;
        
        this.input.style.height = 'auto';
        this.input.style.height = Math.min(this.input.scrollHeight, 120) + 'px';
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    async loadConversationHistory() {
        if (!window.auth?.isAuthenticated()) return;

        try {
            const response = await API.chatbot.getHistory({
                session_id: this.sessionId,
                limit: 10
            });

            // Load recent messages
            response.conversations.reverse().forEach(conv => {
                this.addMessage(conv.message, 'user');
                this.addMessage(conv.response, 'bot');
            });

        } catch (error) {
            console.error('Error loading conversation history:', error);
        }
    }

    clearHistory() {
        if (!this.messagesContainer) return;
        
        // Keep only the welcome message
        const messages = this.messagesContainer.querySelectorAll('.message');
        messages.forEach((message, index) => {
            if (index > 0) { // Keep first message (welcome)
                message.remove();
            }
        });

        // Clear server history if authenticated
        if (window.auth?.isAuthenticated()) {
            API.chatbot.clearHistory().catch(console.error);
        }

        // Generate new session ID
        this.sessionId = this.generateSessionId();
    }

    addQuickResponses(category) {
        API.chatbot.getQuickResponses(category)
            .then(response => {
                const quickResponsesDiv = document.createElement('div');
                quickResponsesDiv.className = 'quick-responses';
                
                response.responses.forEach(responseText => {
                    const button = document.createElement('button');
                    button.className = 'quick-response-btn';
                    button.textContent = responseText;
                    button.addEventListener('click', () => {
                        this.input.value = responseText;
                        this.sendMessage();
                        quickResponsesDiv.remove();
                    });
                    quickResponsesDiv.appendChild(button);
                });

                this.messagesContainer.appendChild(quickResponsesDiv);
                this.scrollToBottom();
            })
            .catch(console.error);
    }

    trackEvent(eventName, data = {}) {
        // Track chatbot usage for analytics
        console.log('Chatbot event:', eventName, data);
        
        // Here you could send to analytics service
        // analytics.track(eventName, data);
    }

    setContext(context) {
        // Set user context for personalized responses
        this.userContext = context;
    }

    showWelcomeMessage() {
        const welcomeMessages = [
            "Hello! I'm your AI assistant for pregnancy and baby care. How can I help you today?",
            "Hi there! I'm here to answer your questions about pregnancy, baby care, and health. What would you like to know?",
            "Welcome! I can help you with pregnancy guidance, baby care tips, and health information. What's on your mind?"
        ];

        const randomMessage = welcomeMessages[Math.floor(Math.random() * welcomeMessages.length)];
        this.addMessage(randomMessage, 'bot');
    }
}

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const chatbot = new ChatbotComponent();
    
    // Make chatbot globally available
    window.chatbot = chatbot;
    
    // Add chatbot-specific styles
    if (!document.querySelector('#chatbot-styles')) {
        const style = document.createElement('style');
        style.id = 'chatbot-styles';
        style.textContent = `
            .message-timestamp {
                font-size: 0.75rem;
                color: var(--gray);
                margin-top: 0.25rem;
                text-align: right;
            }
            
            .bot-message .message-timestamp {
                text-align: left;
            }
            
            .typing-indicator .message-content {
                padding: 0.75rem 1rem;
            }
            
            .typing-dots {
                display: flex;
                gap: 0.25rem;
            }
            
            .typing-dots span {
                width: 6px;
                height: 6px;
                background: var(--gray);
                border-radius: 50%;
                animation: typing 1.4s infinite ease-in-out;
            }
            
            .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
            .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
            
            @keyframes typing {
                0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
                40% { transform: scale(1); opacity: 1; }
            }
            
            .urgent-alert {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: var(--radius-md);
                padding: var(--spacing-md);
                margin: var(--spacing-md) 0;
                color: #856404;
            }
            
            .urgent-alert-content {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
            }
            
            .urgent-alert i {
                color: #f39c12;
            }
            
            .quick-responses {
                display: flex;
                flex-wrap: wrap;
                gap: var(--spacing-sm);
                margin: var(--spacing-md) 0;
            }
            
            .quick-response-btn {
                background: var(--light-gray);
                border: 1px solid #ddd;
                border-radius: var(--radius-md);
                padding: var(--spacing-sm) var(--spacing-md);
                font-size: var(--font-size-sm);
                cursor: pointer;
                transition: all var(--transition-normal);
            }
            
            .quick-response-btn:hover {
                background: var(--primary-light);
                border-color: var(--primary-color);
                color: var(--primary-dark);
            }
            
            .error-message {
                background: #f8d7da !important;
                border-left: 4px solid var(--error) !important;
            }
            
            .message.animate-in {
                animation: messageSlideIn 0.3s ease-out;
            }
            
            @keyframes messageSlideIn {
                from {
                    opacity: 0;
                    transform: translateY(10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    }
});
