/**
 * Main JavaScript file for Preg and Baby Care application
 * Handles global functionality, initialization, and common features
 */

class App {
    constructor() {
        this.isLoading = false;
        this.currentUser = null;
        
        this.init();
    }

    init() {
        this.setupGlobalEventListeners();
        this.initializeComponents();
        this.setupErrorHandling();
        this.checkAuthState();
    }

    setupGlobalEventListeners() {
        // Page load event
        window.addEventListener('load', () => {
            this.hidePageLoader();
            this.initializeAnimations();
        });

        // Before unload event
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        // Online/offline status
        window.addEventListener('online', () => {
            this.showNotification('Connection restored', 'success');
        });

        window.addEventListener('offline', () => {
            this.showNotification('Connection lost. Some features may not work.', 'warning');
        });

        // Handle external links
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="http"]');
            if (link && !link.hasAttribute('target')) {
                link.setAttribute('target', '_blank');
                link.setAttribute('rel', 'noopener noreferrer');
            }
        });
    }

    initializeComponents() {
        // Initialize smooth scrolling
        this.initSmoothScrolling();
        
        // Initialize lazy loading
        this.initLazyLoading();
        
        // Initialize tooltips
        this.initTooltips();
        
        // Initialize modals
        this.initModals();
        
        // Initialize form validation
        this.initFormValidation();
    }

    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
            this.handleError(e.error);
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
            this.handleError(e.reason);
        });
    }

    checkAuthState() {
        if (window.auth) {
            this.currentUser = window.auth.getCurrentUser();
            
            // Listen for auth changes
            window.auth.onAuthChange((event, user) => {
                this.currentUser = user;
                this.handleAuthChange(event, user);
            });
        }
    }

    handleAuthChange(event, user) {
        switch (event) {
            case 'login':
                this.showNotification('Welcome back!', 'success');
                break;
            case 'logout':
                this.showNotification('You have been logged out', 'info');
                break;
            case 'register':
                this.showNotification('Account created successfully!', 'success');
                break;
        }
    }

    initSmoothScrolling() {
        // Smooth scroll for anchor links
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="#"]');
            if (!link) return;

            const targetId = link.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                e.preventDefault();
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }

    initLazyLoading() {
        // Lazy load images
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for older browsers
            images.forEach(img => {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
            });
        }
    }

    initTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.dataset.tooltip);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        tooltip.id = 'global-tooltip';
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        
        setTimeout(() => tooltip.classList.add('show'), 10);
    }

    hideTooltip() {
        const tooltip = document.getElementById('global-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    initModals() {
        // Modal triggers
        document.addEventListener('click', (e) => {
            const trigger = e.target.closest('[data-modal]');
            if (trigger) {
                e.preventDefault();
                const modalId = trigger.dataset.modal;
                this.openModal(modalId);
            }
            
            // Close modal
            const closeBtn = e.target.closest('.modal-close, .modal-backdrop');
            if (closeBtn) {
                this.closeModal();
            }
        });
        
        // Close modal on escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }

    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal() {
        const activeModal = document.querySelector('.modal.active');
        if (activeModal) {
            activeModal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    initFormValidation() {
        const forms = document.querySelectorAll('form[data-validate]');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                }
            });
            
            // Real-time validation
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
            });
        });
    }

    validateForm(form) {
        const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    }

    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        let isValid = true;
        let message = '';
        
        // Required validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'This field is required';
        }
        
        // Email validation
        if (type === 'email' && value && !CONFIG.VALIDATION.EMAIL_REGEX.test(value)) {
            isValid = false;
            message = 'Please enter a valid email address';
        }
        
        // Password validation
        if (type === 'password' && value && value.length < CONFIG.VALIDATION.PASSWORD_MIN_LENGTH) {
            isValid = false;
            message = `Password must be at least ${CONFIG.VALIDATION.PASSWORD_MIN_LENGTH} characters`;
        }
        
        this.showFieldValidation(field, isValid, message);
        return isValid;
    }

    showFieldValidation(field, isValid, message) {
        // Remove existing validation
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        
        field.classList.remove('field-valid', 'field-invalid');
        
        if (!isValid && message) {
            field.classList.add('field-invalid');
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        } else if (isValid && field.value.trim()) {
            field.classList.add('field-valid');
        }
    }

    initializeAnimations() {
        // Intersection Observer for animations
        if ('IntersectionObserver' in window) {
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                        animationObserver.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });

            // Observe elements with animation classes
            document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right').forEach(el => {
                animationObserver.observe(el);
            });
        }
    }

    showNotification(message, type = 'info', duration = 5000) {
        if (window.navbar) {
            window.navbar.showNotification(message, type);
        } else {
            // Fallback notification
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    showPageLoader() {
        this.isLoading = true;
        
        let loader = document.getElementById('page-loader');
        if (!loader) {
            loader = document.createElement('div');
            loader.id = 'page-loader';
            loader.innerHTML = `
                <div class="loader-content">
                    <div class="spinner"></div>
                    <p>Loading...</p>
                </div>
            `;
            document.body.appendChild(loader);
        }
        
        loader.style.display = 'flex';
    }

    hidePageLoader() {
        this.isLoading = false;
        
        const loader = document.getElementById('page-loader');
        if (loader) {
            loader.style.display = 'none';
        }
    }

    handleError(error) {
        console.error('Application error:', error);
        
        // Don't show error notifications for network errors in development
        if (error.message && error.message.includes('fetch')) {
            return;
        }
        
        this.showNotification('An error occurred. Please try again.', 'error');
    }

    cleanup() {
        // Cleanup before page unload
        if (this.currentUser) {
            // Save any pending data
        }
    }

    // Utility methods
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        return new Date(date).toLocaleDateString('en-US', { ...defaultOptions, ...options });
    }

    formatTime(date) {
        return new Date(date).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const app = new App();
    
    // Make app globally available
    window.app = app;
});

// Add global styles for components
if (!document.querySelector('#global-component-styles')) {
    const style = document.createElement('style');
    style.id = 'global-component-styles';
    style.textContent = `
        .tooltip {
            position: absolute;
            background: var(--dark-gray);
            color: var(--white);
            padding: 0.5rem;
            border-radius: var(--radius-sm);
            font-size: var(--font-size-sm);
            z-index: 10000;
            opacity: 0;
            transition: opacity var(--transition-normal);
            pointer-events: none;
        }
        
        .tooltip.show {
            opacity: 1;
        }
        
        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: var(--dark-gray);
        }
        
        .field-error {
            color: var(--error);
            font-size: var(--font-size-sm);
            margin-top: 0.25rem;
        }
        
        .field-valid {
            border-color: var(--success) !important;
        }
        
        .field-invalid {
            border-color: var(--error) !important;
        }
        
        #page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }
        
        .loader-content {
            text-align: center;
        }
        
        .loader-content .spinner {
            width: 40px;
            height: 40px;
            margin: 0 auto 1rem;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-normal);
        }
        
        .modal.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal-content {
            background: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-2xl);
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            transform: scale(0.9);
            transition: transform var(--transition-normal);
        }
        
        .modal.active .modal-content {
            transform: scale(1);
        }
        
        .modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--gray);
        }
        
        .modal-close:hover {
            color: var(--dark-gray);
        }
    `;
    document.head.appendChild(style);
}
