"""
Exercise routes for pregnancy fitness guides and recommendations.
"""

import logging
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

from models.database import get_db
from utils.helpers import handle_error, convert_rows_to_dicts, require_admin

logger = logging.getLogger(__name__)
exercise_bp = Blueprint('exercise', __name__)

@exercise_bp.route('/guides', methods=['GET'])
def get_exercise_guides():
    """Get exercise guides with filtering."""
    try:
        category = request.args.get('category')
        difficulty = request.args.get('difficulty')
        trimester = request.args.get('trimester')
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM exercise_guides WHERE is_active = 1"
            params = []
            
            if category:
                query += " AND category = ?"
                params.append(category)
            
            if difficulty:
                query += " AND difficulty_level = ?"
                params.append(difficulty)
            
            if trimester:
                query += " AND (trimester_safe LIKE ? OR trimester_safe = 'all')"
                params.append(f'%{trimester}%')
            
            query += " ORDER BY category, difficulty_level"
            
            cursor.execute(query, params)
            guides = convert_rows_to_dicts(cursor.fetchall())
            
            return jsonify({'guides': guides}), 200
            
    except Exception as e:
        logger.error(f"Get exercise guides error: {str(e)}")
        return handle_error(e)

@exercise_bp.route('/guides/<int:guide_id>', methods=['GET'])
def get_exercise_guide(guide_id):
    """Get specific exercise guide."""
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM exercise_guides
                WHERE id = ? AND is_active = 1
            """, (guide_id,))
            
            guide = cursor.fetchone()
            
            if not guide:
                return jsonify({'message': 'Exercise guide not found'}), 404
            
            return jsonify({'guide': dict(guide)}), 200
            
    except Exception as e:
        logger.error(f"Get exercise guide error: {str(e)}")
        return handle_error(e)
