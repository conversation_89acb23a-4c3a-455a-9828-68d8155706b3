"""
Chatbot routes for AI-powered pregnancy and baby care assistance.
Integrates with Gemini AI service for intelligent responses.
"""

import logging
import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify, session
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt

from models.database import get_db
from services.gemini_service import gemini_service
from utils.helpers import handle_error, convert_rows_to_dicts

logger = logging.getLogger(__name__)
chatbot_bp = Blueprint('chatbot', __name__)

@chatbot_bp.route('/message', methods=['POST'])
def send_message():
    """Send message to AI chatbot and get response."""
    try:
        data = request.get_json()
        
        if not data.get('message'):
            return jsonify({'message': 'Message is required'}), 400
        
        user_message = data['message'].strip()
        session_id = data.get('session_id') or str(uuid.uuid4())
        
        # Get user context if authenticated
        user_context = None
        user_id = None
        
        try:
            # Try to get user identity if JW<PERSON> token is present
            user_id = get_jwt_identity()
            if user_id:
                user_context = get_user_context(user_id)
        except:
            # User is not authenticated, continue without context
            pass
        
        # Check if Gemini service is available
        if not gemini_service:
            return jsonify({
                'message': 'AI service is currently unavailable. Please try again later.',
                'session_id': session_id
            }), 503
        
        # Analyze message sentiment for appropriate response
        sentiment = gemini_service.analyze_sentiment(user_message)
        
        # Handle urgent situations
        if sentiment == 'urgent':
            ai_response = gemini_service.get_emergency_response()
        else:
            # Generate AI response
            ai_response = gemini_service.generate_response(user_message, user_context)
        
        # Save conversation to database
        save_conversation(user_id, session_id, user_message, ai_response, user_context)
        
        return jsonify({
            'response': ai_response,
            'session_id': session_id,
            'sentiment': sentiment
        }), 200
        
    except Exception as e:
        logger.error(f"Chatbot message error: {str(e)}")
        return handle_error(e)

@chatbot_bp.route('/quick-responses/<category>', methods=['GET'])
def get_quick_responses(category):
    """Get quick response suggestions for a category."""
    try:
        if not gemini_service:
            return jsonify({'responses': []}), 200
        
        responses = gemini_service.get_quick_responses(category)
        
        return jsonify({'responses': responses}), 200
        
    except Exception as e:
        logger.error(f"Get quick responses error: {str(e)}")
        return handle_error(e)

@chatbot_bp.route('/history', methods=['GET'])
@jwt_required()
def get_conversation_history():
    """Get conversation history for authenticated user."""
    try:
        user_id = get_jwt_identity()
        session_id = request.args.get('session_id')
        limit = request.args.get('limit', 50, type=int)
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT session_id, message, response, created_at
                FROM chatbot_conversations
                WHERE user_id = ?
            """
            params = [user_id]
            
            if session_id:
                query += " AND session_id = ?"
                params.append(session_id)
            
            query += " ORDER BY created_at DESC LIMIT ?"
            params.append(limit)
            
            cursor.execute(query, params)
            conversations = convert_rows_to_dicts(cursor.fetchall())
            
            return jsonify({'conversations': conversations}), 200
            
    except Exception as e:
        logger.error(f"Get conversation history error: {str(e)}")
        return handle_error(e)

@chatbot_bp.route('/history', methods=['DELETE'])
@jwt_required()
def clear_conversation_history():
    """Clear conversation history for authenticated user."""
    try:
        user_id = get_jwt_identity()
        session_id = request.args.get('session_id')
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            if session_id:
                # Clear specific session
                cursor.execute("""
                    DELETE FROM chatbot_conversations
                    WHERE user_id = ? AND session_id = ?
                """, (user_id, session_id))
            else:
                # Clear all conversations for user
                cursor.execute("""
                    DELETE FROM chatbot_conversations
                    WHERE user_id = ?
                """, (user_id,))
            
            conn.commit()
            
            logger.info(f"Conversation history cleared for user: {user_id}")
            
            return jsonify({'message': 'Conversation history cleared successfully'}), 200
            
    except Exception as e:
        logger.error(f"Clear conversation history error: {str(e)}")
        return handle_error(e)

@chatbot_bp.route('/feedback', methods=['POST'])
@jwt_required()
def submit_feedback():
    """Submit feedback on chatbot response."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data.get('conversation_id') or not data.get('rating'):
            return jsonify({'message': 'Conversation ID and rating are required'}), 400
        
        # Here you could save feedback to a feedback table
        # For now, just log it
        logger.info(f"Chatbot feedback from user {user_id}: {data}")
        
        return jsonify({'message': 'Feedback submitted successfully'}), 200
        
    except Exception as e:
        logger.error(f"Submit feedback error: {str(e)}")
        return handle_error(e)

def get_user_context(user_id):
    """Get user context for personalized AI responses."""
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get user profile information
            cursor.execute("""
                SELECT u.first_name, p.due_date, p.current_week, 
                       p.medical_conditions, p.allergies
                FROM users u
                LEFT JOIN user_profiles p ON u.id = p.user_id
                WHERE u.id = ?
            """, (user_id,))
            
            user_data = cursor.fetchone()
            
            if not user_data:
                return None
            
            user_data = dict(user_data)
            
            context = {
                'user_name': user_data.get('first_name'),
                'medical_conditions': user_data.get('medical_conditions'),
                'allergies': user_data.get('allergies')
            }
            
            # Determine pregnancy status and trimester
            if user_data.get('due_date'):
                context['is_pregnant'] = True
                context['due_date'] = user_data['due_date']
                
                if user_data.get('current_week'):
                    context['pregnancy_week'] = user_data['current_week']
                    
                    # Determine trimester
                    week = user_data['current_week']
                    if week <= 12:
                        context['trimester'] = 'first'
                    elif week <= 27:
                        context['trimester'] = 'second'
                    else:
                        context['trimester'] = 'third'
            
            # Get recent conversation topics for context
            cursor.execute("""
                SELECT message FROM chatbot_conversations
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT 5
            """, (user_id,))
            
            recent_messages = cursor.fetchall()
            if recent_messages:
                # Extract key topics from recent messages (simplified)
                topics = []
                for msg in recent_messages:
                    message = msg['message'].lower()
                    if 'nutrition' in message or 'food' in message:
                        topics.append('nutrition')
                    elif 'exercise' in message or 'workout' in message:
                        topics.append('exercise')
                    elif 'sleep' in message:
                        topics.append('sleep')
                    elif 'baby' in message:
                        topics.append('baby_care')
                
                context['previous_topics'] = list(set(topics))
            
            return context
            
    except Exception as e:
        logger.error(f"Get user context error: {str(e)}")
        return None

def save_conversation(user_id, session_id, message, response, context):
    """Save conversation to database."""
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO chatbot_conversations (
                    user_id, session_id, message, response, context
                ) VALUES (?, ?, ?, ?, ?)
            """, (
                user_id,
                session_id,
                message,
                response,
                str(context) if context else None
            ))
            
            conn.commit()
            
    except Exception as e:
        logger.error(f"Save conversation error: {str(e)}")

@chatbot_bp.route('/categories', methods=['GET'])
def get_categories():
    """Get available quick response categories."""
    categories = [
        {
            'id': 'nutrition',
            'name': 'Nutrition & Diet',
            'icon': 'utensils',
            'description': 'Questions about pregnancy nutrition and healthy eating'
        },
        {
            'id': 'exercise',
            'name': 'Exercise & Fitness',
            'icon': 'dumbbell',
            'description': 'Safe exercises and fitness during pregnancy'
        },
        {
            'id': 'symptoms',
            'name': 'Symptoms & Health',
            'icon': 'heartbeat',
            'description': 'Common pregnancy symptoms and health concerns'
        },
        {
            'id': 'baby_care',
            'name': 'Baby Care',
            'icon': 'baby',
            'description': 'Newborn care, feeding, and baby development'
        },
        {
            'id': 'development',
            'name': 'Development',
            'icon': 'child',
            'description': 'Baby milestones and developmental stages'
        }
    ]
    
    return jsonify({'categories': categories}), 200
