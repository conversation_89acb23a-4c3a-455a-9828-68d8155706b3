"""
Authentication routes for user registration, login, and profile management.
Handles JWT token generation and validation.
"""

import logging
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from werkzeug.security import generate_password_hash, check_password_hash
import re

from models.database import get_db
from utils.helpers import validate_request, handle_error

logger = logging.getLogger(__name__)
auth_bp = Blueprint('auth', __name__)

def validate_email(email):
    """Validate email format."""
    pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """Validate password strength."""
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r'[A-Za-z]', password):
        return False, "Password must contain at least one letter"
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    return True, "Password is valid"

@auth_bp.route('/register', methods=['POST'])
def register():
    """Register a new user."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'password', 'first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'message': f'{field} is required'}), 400
        
        email = data['email'].lower().strip()
        password = data['password']
        first_name = data['first_name'].strip()
        last_name = data['last_name'].strip()
        phone = data.get('phone', '').strip()
        date_of_birth = data.get('date_of_birth')
        
        # Validate email format
        if not validate_email(email):
            return jsonify({'message': 'Invalid email format'}), 400
        
        # Validate password strength
        is_valid, message = validate_password(password)
        if not is_valid:
            return jsonify({'message': message}), 400
        
        # Validate name length
        if len(first_name) < 2 or len(last_name) < 2:
            return jsonify({'message': 'First name and last name must be at least 2 characters long'}), 400
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check if user already exists
            cursor.execute("SELECT id FROM users WHERE email = ?", (email,))
            if cursor.fetchone():
                return jsonify({'message': 'User with this email already exists'}), 409
            
            # Hash password
            password_hash = generate_password_hash(password)
            
            # Insert new user
            cursor.execute("""
                INSERT INTO users (email, password_hash, first_name, last_name, phone, date_of_birth)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (email, password_hash, first_name, last_name, phone, date_of_birth))
            
            user_id = cursor.lastrowid
            
            # Create user profile
            cursor.execute("""
                INSERT INTO user_profiles (user_id) VALUES (?)
            """, (user_id,))
            
            conn.commit()
            
            # Create access token
            access_token = create_access_token(identity=user_id)
            
            # Get user data for response
            cursor.execute("""
                SELECT id, email, first_name, last_name, phone, date_of_birth, is_admin, created_at
                FROM users WHERE id = ?
            """, (user_id,))
            user = dict(cursor.fetchone())
            
            logger.info(f"New user registered: {email}")
            
            return jsonify({
                'message': 'User registered successfully',
                'access_token': access_token,
                'user': user
            }), 201
            
    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        return handle_error(e)

@auth_bp.route('/login', methods=['POST'])
def login():
    """Authenticate user and return access token."""
    try:
        data = request.get_json()
        
        # Validate required fields
        if not data.get('email') or not data.get('password'):
            return jsonify({'message': 'Email and password are required'}), 400
        
        email = data['email'].lower().strip()
        password = data['password']
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get user by email
            cursor.execute("""
                SELECT id, email, password_hash, first_name, last_name, phone, 
                       date_of_birth, is_admin, is_active, email_verified
                FROM users WHERE email = ?
            """, (email,))
            
            user = cursor.fetchone()
            
            if not user:
                return jsonify({'message': 'Invalid email or password'}), 401
            
            user = dict(user)
            
            # Check if user is active
            if not user['is_active']:
                return jsonify({'message': 'Account is deactivated'}), 401
            
            # Verify password
            if not check_password_hash(user['password_hash'], password):
                return jsonify({'message': 'Invalid email or password'}), 401
            
            # Create access token
            access_token = create_access_token(identity=user['id'])
            
            # Remove password hash from response
            del user['password_hash']
            
            logger.info(f"User logged in: {email}")
            
            return jsonify({
                'message': 'Login successful',
                'access_token': access_token,
                'user': user
            }), 200
            
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return handle_error(e)

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """Get current user profile."""
    try:
        user_id = get_jwt_identity()
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get user and profile data
            cursor.execute("""
                SELECT u.id, u.email, u.first_name, u.last_name, u.phone, u.date_of_birth,
                       u.is_admin, u.created_at,
                       p.due_date, p.current_week, p.pre_pregnancy_weight, p.height,
                       p.blood_type, p.allergies, p.medical_conditions,
                       p.emergency_contact_name, p.emergency_contact_phone,
                       p.doctor_name, p.doctor_phone, p.hospital_name
                FROM users u
                LEFT JOIN user_profiles p ON u.id = p.user_id
                WHERE u.id = ?
            """, (user_id,))
            
            user = cursor.fetchone()
            
            if not user:
                return jsonify({'message': 'User not found'}), 404
            
            return jsonify({'user': dict(user)}), 200
            
    except Exception as e:
        logger.error(f"Get profile error: {str(e)}")
        return handle_error(e)

@auth_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """Update user profile."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Update user table
            user_fields = ['first_name', 'last_name', 'phone', 'date_of_birth']
            user_updates = []
            user_values = []
            
            for field in user_fields:
                if field in data:
                    user_updates.append(f"{field} = ?")
                    user_values.append(data[field])
            
            if user_updates:
                user_values.append(datetime.utcnow())
                user_values.append(user_id)
                cursor.execute(f"""
                    UPDATE users 
                    SET {', '.join(user_updates)}, updated_at = ?
                    WHERE id = ?
                """, user_values)
            
            # Update profile table
            profile_fields = [
                'due_date', 'current_week', 'pre_pregnancy_weight', 'height',
                'blood_type', 'allergies', 'medical_conditions',
                'emergency_contact_name', 'emergency_contact_phone',
                'doctor_name', 'doctor_phone', 'hospital_name'
            ]
            
            profile_updates = []
            profile_values = []
            
            for field in profile_fields:
                if field in data:
                    profile_updates.append(f"{field} = ?")
                    profile_values.append(data[field])
            
            if profile_updates:
                profile_values.append(datetime.utcnow())
                profile_values.append(user_id)
                cursor.execute(f"""
                    UPDATE user_profiles 
                    SET {', '.join(profile_updates)}, updated_at = ?
                    WHERE user_id = ?
                """, profile_values)
            
            conn.commit()
            
            logger.info(f"Profile updated for user: {user_id}")
            
            return jsonify({'message': 'Profile updated successfully'}), 200
            
    except Exception as e:
        logger.error(f"Update profile error: {str(e)}")
        return handle_error(e)

@auth_bp.route('/change-password', methods=['POST'])
@jwt_required()
def change_password():
    """Change user password."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        # Validate required fields
        if not data.get('current_password') or not data.get('new_password'):
            return jsonify({'message': 'Current password and new password are required'}), 400
        
        current_password = data['current_password']
        new_password = data['new_password']
        
        # Validate new password strength
        is_valid, message = validate_password(new_password)
        if not is_valid:
            return jsonify({'message': message}), 400
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get current password hash
            cursor.execute("SELECT password_hash FROM users WHERE id = ?", (user_id,))
            user = cursor.fetchone()
            
            if not user:
                return jsonify({'message': 'User not found'}), 404
            
            # Verify current password
            if not check_password_hash(user['password_hash'], current_password):
                return jsonify({'message': 'Current password is incorrect'}), 400
            
            # Hash new password
            new_password_hash = generate_password_hash(new_password)
            
            # Update password
            cursor.execute("""
                UPDATE users 
                SET password_hash = ?, updated_at = ?
                WHERE id = ?
            """, (new_password_hash, datetime.utcnow(), user_id))
            
            conn.commit()
            
            logger.info(f"Password changed for user: {user_id}")
            
            return jsonify({'message': 'Password changed successfully'}), 200
            
    except Exception as e:
        logger.error(f"Change password error: {str(e)}")
        return handle_error(e)

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """Logout user (client-side token removal)."""
    try:
        user_id = get_jwt_identity()
        logger.info(f"User logged out: {user_id}")
        
        return jsonify({'message': 'Logout successful'}), 200
        
    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        return handle_error(e)
