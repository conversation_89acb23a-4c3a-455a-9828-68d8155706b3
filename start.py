#!/usr/bin/env python3
"""
Startup script for Preg and Baby Care HTML/CSS/JS + Python application.
This script initializes the database, starts the Flask backend, and opens the frontend.
"""

import os
import sys
import subprocess
import webbrowser
import time
import threading
from pathlib import Path

def print_banner():
    """Print application banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                    Preg and Baby Care                        ║
    ║                HTML/CSS/JS + Python Version                  ║
    ║                                                              ║
    ║    Comprehensive pregnancy and baby care guidance            ║
    ║    with AI-powered assistance                                ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required.")
        print(f"   Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version.split()[0]}")

def check_dependencies():
    """Check if required dependencies are installed."""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        'flask',
        'flask-cors',
        'flask-jwt-extended',
        'python-dotenv',
        'bcrypt',
        'google-generativeai'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("   Please install them using:")
        print(f"   pip install {' '.join(missing_packages)}")
        
        # Ask user if they want to install automatically
        response = input("\nWould you like to install missing packages automatically? (y/n): ")
        if response.lower() == 'y':
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
                print("✅ Dependencies installed successfully!")
            except subprocess.CalledProcessError:
                print("❌ Failed to install dependencies. Please install manually.")
                sys.exit(1)
        else:
            sys.exit(1)

def setup_environment():
    """Setup environment variables."""
    print("\n🔧 Setting up environment...")
    
    backend_dir = Path(__file__).parent / 'backend'
    env_file = backend_dir / '.env'
    env_example = backend_dir / '.env.example'
    
    if not env_file.exists() and env_example.exists():
        print("📝 Creating .env file from .env.example...")
        with open(env_example, 'r') as f:
            content = f.read()
        
        with open(env_file, 'w') as f:
            f.write(content)
        
        print("✅ .env file created")
        print("⚠️  Please update the .env file with your actual API keys and settings")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("⚠️  No .env.example file found")

def initialize_database():
    """Initialize the database with sample data."""
    print("\n🗄️  Initializing database...")
    
    backend_dir = Path(__file__).parent / 'backend'
    init_script = backend_dir / 'database' / 'init_sample_data.py'
    
    if init_script.exists():
        try:
            # Change to backend directory
            original_cwd = os.getcwd()
            os.chdir(backend_dir)
            
            # Run initialization script
            subprocess.check_call([sys.executable, str(init_script)])
            print("✅ Database initialized successfully")
            
            # Restore original directory
            os.chdir(original_cwd)
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Database initialization failed: {e}")
            return False
    else:
        print("⚠️  Database initialization script not found")
        return False
    
    return True

def start_backend():
    """Start the Flask backend server."""
    print("\n🚀 Starting Flask backend server...")
    
    backend_dir = Path(__file__).parent / 'backend'
    app_file = backend_dir / 'app.py'
    
    if not app_file.exists():
        print("❌ Backend app.py not found")
        return None
    
    try:
        # Change to backend directory
        os.chdir(backend_dir)
        
        # Start Flask server
        env = os.environ.copy()
        env['FLASK_ENV'] = 'development'
        env['FLASK_DEBUG'] = 'True'
        
        process = subprocess.Popen(
            [sys.executable, str(app_file)],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        print("✅ Backend server starting...")
        return process
        
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
        return None

def start_frontend():
    """Start the frontend server."""
    print("\n🌐 Starting frontend server...")
    
    frontend_dir = Path(__file__).parent / 'frontend'
    
    if not frontend_dir.exists():
        print("❌ Frontend directory not found")
        return None
    
    try:
        # Change to frontend directory
        os.chdir(frontend_dir)
        
        # Start simple HTTP server
        process = subprocess.Popen(
            [sys.executable, '-m', 'http.server', '8000'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        print("✅ Frontend server starting on http://localhost:8000")
        return process
        
    except Exception as e:
        print(f"❌ Failed to start frontend: {e}")
        return None

def wait_for_server(url, timeout=30):
    """Wait for server to be ready."""
    import urllib.request
    import urllib.error
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            urllib.request.urlopen(url, timeout=1)
            return True
        except (urllib.error.URLError, OSError):
            time.sleep(1)
    return False

def open_browser():
    """Open the application in the default browser."""
    print("\n🌐 Opening application in browser...")
    
    # Wait for frontend server to be ready
    if wait_for_server('http://localhost:8000', timeout=10):
        webbrowser.open('http://localhost:8000')
        print("✅ Application opened in browser")
    else:
        print("⚠️  Frontend server not ready, please open http://localhost:8000 manually")

def monitor_processes(backend_process, frontend_process):
    """Monitor backend and frontend processes."""
    try:
        while True:
            # Check if processes are still running
            if backend_process and backend_process.poll() is not None:
                print("❌ Backend process stopped")
                break
            
            if frontend_process and frontend_process.poll() is not None:
                print("❌ Frontend process stopped")
                break
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Shutting down servers...")
        
        if backend_process:
            backend_process.terminate()
            backend_process.wait()
            print("✅ Backend server stopped")
        
        if frontend_process:
            frontend_process.terminate()
            frontend_process.wait()
            print("✅ Frontend server stopped")

def main():
    """Main startup function."""
    print_banner()
    
    # Check system requirements
    check_python_version()
    check_dependencies()
    
    # Setup environment
    setup_environment()
    
    # Initialize database
    if not initialize_database():
        print("⚠️  Continuing without database initialization...")
    
    # Start servers
    backend_process = start_backend()
    if not backend_process:
        print("❌ Failed to start backend server")
        sys.exit(1)
    
    # Wait a moment for backend to start
    time.sleep(3)
    
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ Failed to start frontend server")
        if backend_process:
            backend_process.terminate()
        sys.exit(1)
    
    # Wait a moment for frontend to start
    time.sleep(2)
    
    # Open browser
    threading.Thread(target=open_browser, daemon=True).start()
    
    print("\n" + "="*60)
    print("🎉 Preg and Baby Care is now running!")
    print("="*60)
    print("📱 Frontend: http://localhost:8000")
    print("🔧 Backend API: http://localhost:5000")
    print("📚 API Documentation: http://localhost:5000/api/info")
    print("="*60)
    print("👤 Demo Admin Login:")
    print("   Email: <EMAIL>")
    print("   Password: admin123")
    print("="*60)
    print("Press Ctrl+C to stop the servers")
    print("="*60)
    
    # Monitor processes
    monitor_processes(backend_process, frontend_process)

if __name__ == "__main__":
    main()
