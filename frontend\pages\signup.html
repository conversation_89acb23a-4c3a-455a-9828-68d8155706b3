<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Preg and Baby Care</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/responsive.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            padding: var(--spacing-lg);
        }
        
        .auth-card {
            background: var(--white);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-2xl);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }
        
        .auth-logo {
            color: var(--primary-color);
            font-size: var(--font-size-3xl);
            margin-bottom: var(--spacing-sm);
        }
        
        .auth-title {
            color: var(--dark-gray);
            margin-bottom: var(--spacing-lg);
        }
        
        .auth-subtitle {
            color: var(--gray);
            margin-bottom: var(--spacing-2xl);
        }
        
        .auth-form {
            text-align: left;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }
        
        .auth-links {
            text-align: center;
            margin-top: var(--spacing-lg);
        }
        
        .auth-links a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .auth-links a:hover {
            text-decoration: underline;
        }
        
        .password-strength {
            margin-top: var(--spacing-sm);
            font-size: var(--font-size-sm);
        }
        
        .strength-bar {
            height: 4px;
            background: #e0e0e0;
            border-radius: 2px;
            margin-top: var(--spacing-xs);
            overflow: hidden;
        }
        
        .strength-fill {
            height: 100%;
            transition: all var(--transition-normal);
            border-radius: 2px;
        }
        
        .strength-weak { background: var(--error); width: 25%; }
        .strength-fair { background: var(--warning); width: 50%; }
        .strength-good { background: var(--secondary-color); width: 75%; }
        .strength-strong { background: var(--success); width: 100%; }
        
        .terms-checkbox {
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-sm);
            margin: var(--spacing-lg) 0;
        }
        
        .terms-checkbox input {
            margin-top: 4px;
        }
        
        .terms-checkbox label {
            font-size: var(--font-size-sm);
            line-height: 1.5;
            margin-bottom: 0;
        }
        
        @media (max-width: 576px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-logo">
                <i class="fas fa-baby"></i>
            </div>
            <h1 class="auth-title">Create Account</h1>
            <p class="auth-subtitle">Join Preg and Baby Care for personalized guidance</p>
            
            <!-- Alert Messages -->
            <div id="alert-container"></div>
            
            <!-- Signup Form -->
            <form class="auth-form" id="signup-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="first-name" class="form-label">
                            <i class="fas fa-user"></i> First Name
                        </label>
                        <input 
                            type="text" 
                            id="first-name" 
                            name="first_name" 
                            class="form-input" 
                            placeholder="Enter first name"
                            required
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="last-name" class="form-label">
                            <i class="fas fa-user"></i> Last Name
                        </label>
                        <input 
                            type="text" 
                            id="last-name" 
                            name="last_name" 
                            class="form-input" 
                            placeholder="Enter last name"
                            required
                        >
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="email" class="form-label">
                        <i class="fas fa-envelope"></i> Email Address
                    </label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-input" 
                        placeholder="Enter your email"
                        required
                    >
                </div>
                
                <div class="form-group">
                    <label for="phone" class="form-label">
                        <i class="fas fa-phone"></i> Phone Number (Optional)
                    </label>
                    <input 
                        type="tel" 
                        id="phone" 
                        name="phone" 
                        class="form-input" 
                        placeholder="Enter your phone number"
                    >
                </div>
                
                <div class="form-group">
                    <label for="date-of-birth" class="form-label">
                        <i class="fas fa-calendar"></i> Date of Birth (Optional)
                    </label>
                    <input 
                        type="date" 
                        id="date-of-birth" 
                        name="date_of_birth" 
                        class="form-input"
                    >
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i> Password
                    </label>
                    <div style="position: relative;">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input" 
                            placeholder="Create a strong password"
                            required
                        >
                        <button 
                            type="button" 
                            id="toggle-password"
                            style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--gray); cursor: pointer;"
                        >
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="password-strength">
                        <div class="strength-bar">
                            <div class="strength-fill" id="strength-fill"></div>
                        </div>
                        <div id="strength-text" style="margin-top: var(--spacing-xs); color: var(--gray);">
                            Password strength: <span id="strength-level">-</span>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="confirm-password" class="form-label">
                        <i class="fas fa-lock"></i> Confirm Password
                    </label>
                    <input 
                        type="password" 
                        id="confirm-password" 
                        name="confirm_password" 
                        class="form-input" 
                        placeholder="Confirm your password"
                        required
                    >
                    <div id="password-match" style="margin-top: var(--spacing-xs); font-size: var(--font-size-sm);"></div>
                </div>
                
                <div class="terms-checkbox">
                    <input type="checkbox" id="terms" required>
                    <label for="terms">
                        I agree to the <a href="terms.html" target="_blank">Terms of Service</a> 
                        and <a href="privacy-policy.html" target="_blank">Privacy Policy</a>
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary" style="width: 100%;" id="signup-btn">
                    <span id="signup-text">Create Account</span>
                    <span id="signup-spinner" class="spinner" style="display: none;"></span>
                </button>
            </form>
            
            <div class="auth-links">
                <p>Already have an account? <a href="login.html">Sign in here</a></p>
                <p><a href="../index.html">← Back to Home</a></p>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="../js/utils/config.js"></script>
    <script src="../js/utils/api.js"></script>
    <script src="../js/utils/auth.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const signupForm = document.getElementById('signup-form');
            const signupBtn = document.getElementById('signup-btn');
            const signupText = document.getElementById('signup-text');
            const signupSpinner = document.getElementById('signup-spinner');
            const togglePassword = document.getElementById('toggle-password');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirm-password');
            const alertContainer = document.getElementById('alert-container');
            const strengthFill = document.getElementById('strength-fill');
            const strengthLevel = document.getElementById('strength-level');
            const passwordMatch = document.getElementById('password-match');
            
            // Toggle password visibility
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
            
            // Password strength checker
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = checkPasswordStrength(password);
                
                strengthFill.className = 'strength-fill';
                strengthFill.classList.add(`strength-${strength.level}`);
                strengthLevel.textContent = strength.text;
                strengthLevel.style.color = strength.color;
            });
            
            // Password match checker
            confirmPasswordInput.addEventListener('input', function() {
                const password = passwordInput.value;
                const confirmPassword = this.value;
                
                if (confirmPassword === '') {
                    passwordMatch.textContent = '';
                    return;
                }
                
                if (password === confirmPassword) {
                    passwordMatch.textContent = '✓ Passwords match';
                    passwordMatch.style.color = 'var(--success)';
                } else {
                    passwordMatch.textContent = '✗ Passwords do not match';
                    passwordMatch.style.color = 'var(--error)';
                }
            });
            
            // Handle form submission
            signupForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = new FormData(signupForm);
                const userData = {
                    first_name: formData.get('first_name'),
                    last_name: formData.get('last_name'),
                    email: formData.get('email'),
                    phone: formData.get('phone'),
                    date_of_birth: formData.get('date_of_birth'),
                    password: formData.get('password')
                };
                
                // Validate passwords match
                if (userData.password !== formData.get('confirm_password')) {
                    showAlert('Passwords do not match', 'error');
                    return;
                }
                
                // Show loading state
                signupBtn.disabled = true;
                signupText.style.display = 'none';
                signupSpinner.style.display = 'inline-block';
                
                try {
                    const response = await API.auth.register(userData);
                    
                    // Store authentication data
                    localStorage.setItem(CONFIG.AUTH.TOKEN_KEY, response.access_token);
                    localStorage.setItem(CONFIG.AUTH.USER_KEY, JSON.stringify(response.user));
                    
                    // Show success message
                    showAlert('Account created successfully! Redirecting...', 'success');
                    
                    // Redirect to home page
                    setTimeout(() => {
                        window.location.href = '../index.html';
                    }, 1500);
                    
                } catch (error) {
                    console.error('Signup error:', error);
                    showAlert(error.message || 'Registration failed. Please try again.', 'error');
                } finally {
                    // Reset loading state
                    signupBtn.disabled = false;
                    signupText.style.display = 'inline';
                    signupSpinner.style.display = 'none';
                }
            });
            
            function checkPasswordStrength(password) {
                let score = 0;
                
                if (password.length >= 8) score++;
                if (/[a-z]/.test(password)) score++;
                if (/[A-Z]/.test(password)) score++;
                if (/[0-9]/.test(password)) score++;
                if (/[^A-Za-z0-9]/.test(password)) score++;
                
                const levels = [
                    { level: 'weak', text: 'Weak', color: 'var(--error)' },
                    { level: 'weak', text: 'Weak', color: 'var(--error)' },
                    { level: 'fair', text: 'Fair', color: 'var(--warning)' },
                    { level: 'good', text: 'Good', color: 'var(--secondary-color)' },
                    { level: 'strong', text: 'Strong', color: 'var(--success)' }
                ];
                
                return levels[score] || levels[0];
            }
            
            function showAlert(message, type) {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type}`;
                alertDiv.textContent = message;
                
                alertContainer.innerHTML = '';
                alertContainer.appendChild(alertDiv);
                
                // Auto-remove after 5 seconds
                setTimeout(() => {
                    alertDiv.remove();
                }, 5000);
            }
            
            // Check if user is already logged in
            const token = localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
            if (token) {
                window.location.href = '../index.html';
            }
        });
    </script>
</body>
</html>
