/* Mobile First Responsive Design */

/* Large screens (desktops, 1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .hero-subtitle {
        font-size: 1.5rem;
    }
}

/* Medium screens (tablets, 768px and up) */
@media (max-width: 1199px) {
    .container {
        max-width: 960px;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Small screens (landscape phones, 576px and up) */
@media (max-width: 991px) {
    .container {
        max-width: 720px;
        padding: 0 var(--spacing-lg);
    }
    
    /* Navigation */
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--white);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        transition: left var(--transition-normal);
        box-shadow: var(--shadow-lg);
        z-index: 999;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-list {
        flex-direction: column;
        width: 100%;
        padding: var(--spacing-xl) 0;
    }
    
    .nav-item {
        width: 100%;
        margin: 0;
        text-align: center;
    }
    
    .nav-link {
        padding: var(--spacing-lg);
        width: 100%;
        justify-content: center;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background: var(--light-gray);
        margin-top: var(--spacing-sm);
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-auth {
        order: -1;
    }
    
    /* Hero Section */
    .hero {
        height: 80vh;
        margin-top: 80px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-large {
        width: 100%;
        max-width: 300px;
    }
    
    /* Features */
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .feature-card {
        padding: var(--spacing-xl);
    }
    
    /* Stats */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
    
    /* Footer */
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }
}

/* Extra small screens (portrait phones, less than 576px) */
@media (max-width: 575px) {
    .container {
        max-width: 100%;
        padding: 0 var(--spacing-md);
    }
    
    /* Typography */
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    h4 { font-size: 1.25rem; }
    
    /* Navigation */
    .nav-container {
        padding: var(--spacing-md);
    }
    
    .nav-logo span {
        display: none;
    }
    
    .nav-logo i {
        margin-right: 0;
    }
    
    /* Hero */
    .hero {
        height: 70vh;
    }
    
    .hero-title {
        font-size: 2rem;
        margin-bottom: var(--spacing-md);
    }
    
    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: var(--spacing-xl);
    }
    
    .hero-content {
        padding: 0 var(--spacing-md);
    }
    
    /* Buttons */
    .btn {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }
    
    .btn-large {
        padding: var(--spacing-lg) var(--spacing-xl);
        font-size: var(--font-size-base);
    }
    
    /* Features */
    .feature-card {
        padding: var(--spacing-lg);
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }
    
    /* Stats */
    .stats {
        padding: var(--spacing-xl) 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .stat-label {
        font-size: var(--font-size-base);
    }
    
    /* Footer */
    .footer {
        padding: var(--spacing-xl) 0 var(--spacing-md);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .social-links {
        justify-content: center;
    }
    
    /* Chatbot */
    .chatbot-container {
        width: calc(100vw - 40px);
        height: 400px;
        bottom: 80px;
        right: 20px;
        left: 20px;
    }
    
    .chatbot-toggle {
        bottom: 15px;
        right: 15px;
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    /* Forms */
    .form-input,
    .form-select,
    .form-textarea {
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    /* Cards */
    .card {
        padding: var(--spacing-lg);
    }
    
    /* Sections */
    .section {
        padding: var(--spacing-xl) 0;
    }
    
    .section-title {
        margin-bottom: var(--spacing-xl);
    }
}

/* Landscape orientation adjustments */
@media (max-width: 991px) and (orientation: landscape) {
    .hero {
        height: 100vh;
    }
    
    .nav-menu {
        height: calc(100vh - 60px);
    }
    
    .nav-container {
        padding: var(--spacing-sm) var(--spacing-lg);
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-background img {
        image-rendering: -webkit-optimize-contrast;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1a1a1a;
        --light-gray: #2d2d2d;
        --gray: #666666;
        --dark-gray: #e0e0e0;
        --black: #ffffff;
    }
    
    .navbar {
        background: rgba(26, 26, 26, 0.95);
    }
    
    .card {
        background: #2d2d2d;
    }
    
    .form-input {
        background: #2d2d2d;
        color: #e0e0e0;
        border-color: #444;
    }
    
    .chatbot-container {
        background: #2d2d2d;
    }
    
    .bot-message {
        background: #444;
        color: #e0e0e0;
    }
}

/* Print styles */
@media print {
    .navbar,
    .chatbot-toggle,
    .chatbot-container,
    .footer {
        display: none !important;
    }
    
    .hero {
        margin-top: 0;
        height: auto;
        page-break-after: always;
    }
    
    .hero-background {
        display: none;
    }
    
    .hero-content {
        color: var(--black);
    }
    
    * {
        box-shadow: none !important;
    }
}
