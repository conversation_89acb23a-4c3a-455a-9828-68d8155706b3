/**
 * Navigation bar component functionality
 * Handles mobile menu, scroll effects, and user authentication state
 */

class NavbarComponent {
    constructor() {
        this.navbar = document.getElementById('navbar');
        this.navToggle = document.getElementById('nav-toggle');
        this.navMenu = document.getElementById('nav-menu');
        this.isMenuOpen = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupScrollEffect();
        this.setupDropdowns();
        this.setActiveNavItem();
    }

    setupEventListeners() {
        // Mobile menu toggle
        if (this.navToggle) {
            this.navToggle.addEventListener('click', () => {
                this.toggleMobileMenu();
            });
        }

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isMenuOpen && !this.navbar.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Close menu when clicking on nav links (mobile)
        const navLinks = this.navMenu?.querySelectorAll('.nav-link');
        navLinks?.forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth <= 991) {
                    this.closeMobileMenu();
                }
            });
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 991 && this.isMenuOpen) {
                this.closeMobileMenu();
            }
        });
    }

    setupScrollEffect() {
        let lastScrollY = window.scrollY;
        
        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            
            if (this.navbar) {
                // Add scrolled class for styling
                if (currentScrollY > 50) {
                    this.navbar.classList.add('scrolled');
                } else {
                    this.navbar.classList.remove('scrolled');
                }
                
                // Hide/show navbar on scroll (optional)
                if (currentScrollY > lastScrollY && currentScrollY > 100) {
                    // Scrolling down
                    this.navbar.style.transform = 'translateY(-100%)';
                } else {
                    // Scrolling up
                    this.navbar.style.transform = 'translateY(0)';
                }
            }
            
            lastScrollY = currentScrollY;
        });
    }

    setupDropdowns() {
        const dropdowns = document.querySelectorAll('.dropdown');
        
        dropdowns.forEach(dropdown => {
            const toggle = dropdown.querySelector('.dropdown-toggle');
            const menu = dropdown.querySelector('.dropdown-menu');
            
            if (!toggle || !menu) return;
            
            // Desktop hover behavior
            if (window.innerWidth > 991) {
                dropdown.addEventListener('mouseenter', () => {
                    this.showDropdown(menu);
                });
                
                dropdown.addEventListener('mouseleave', () => {
                    this.hideDropdown(menu);
                });
            }
            
            // Mobile click behavior
            toggle.addEventListener('click', (e) => {
                if (window.innerWidth <= 991) {
                    e.preventDefault();
                    this.toggleDropdown(menu);
                }
            });
        });
    }

    showDropdown(menu) {
        menu.style.opacity = '1';
        menu.style.visibility = 'visible';
        menu.style.transform = 'translateY(0)';
    }

    hideDropdown(menu) {
        menu.style.opacity = '0';
        menu.style.visibility = 'hidden';
        menu.style.transform = 'translateY(-10px)';
    }

    toggleDropdown(menu) {
        const isVisible = menu.style.opacity === '1';
        
        if (isVisible) {
            this.hideDropdown(menu);
        } else {
            // Hide other dropdowns first
            document.querySelectorAll('.dropdown-menu').forEach(otherMenu => {
                if (otherMenu !== menu) {
                    this.hideDropdown(otherMenu);
                }
            });
            
            this.showDropdown(menu);
        }
    }

    toggleMobileMenu() {
        if (this.isMenuOpen) {
            this.closeMobileMenu();
        } else {
            this.openMobileMenu();
        }
    }

    openMobileMenu() {
        if (!this.navMenu || !this.navToggle) return;
        
        this.navMenu.classList.add('active');
        this.navToggle.classList.add('active');
        this.isMenuOpen = true;
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }

    closeMobileMenu() {
        if (!this.navMenu || !this.navToggle) return;
        
        this.navMenu.classList.remove('active');
        this.navToggle.classList.remove('active');
        this.isMenuOpen = false;
        
        // Restore body scroll
        document.body.style.overflow = '';
        
        // Close all dropdowns
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            this.hideDropdown(menu);
        });
    }

    setActiveNavItem() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            
            const href = link.getAttribute('href');
            if (href && (currentPath.includes(href) || 
                (href === '../index.html' && currentPath === '/') ||
                (href === 'index.html' && currentPath === '/'))) {
                link.classList.add('active');
            }
        });
    }

    updateAuthState(isAuthenticated, user) {
        const authButtons = document.getElementById('auth-buttons');
        const userMenu = document.getElementById('user-menu');
        const userName = document.getElementById('user-name');
        const adminLink = document.getElementById('admin-link');

        if (!authButtons || !userMenu) return;

        if (isAuthenticated && user) {
            // Show user menu, hide auth buttons
            authButtons.style.display = 'none';
            userMenu.style.display = 'block';
            
            // Update user name
            if (userName) {
                userName.textContent = `${user.first_name} ${user.last_name}`;
            }
            
            // Show/hide admin link
            if (adminLink) {
                adminLink.style.display = user.is_admin ? 'block' : 'none';
            }
        } else {
            // Show auth buttons, hide user menu
            authButtons.style.display = 'flex';
            userMenu.style.display = 'none';
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span>${message}</span>
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            this.removeNotification(notification);
        }, 5000);
        
        // Close button handler
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.removeNotification(notification);
        });
    }

    removeNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    highlightNavItem(selector) {
        // Remove existing highlights
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // Add highlight to specified item
        const targetLink = document.querySelector(selector);
        if (targetLink) {
            targetLink.classList.add('active');
        }
    }

    addNavItem(text, href, position = 'end') {
        const navList = document.querySelector('.nav-list');
        if (!navList) return;
        
        const navItem = document.createElement('li');
        navItem.className = 'nav-item';
        navItem.innerHTML = `<a href="${href}" class="nav-link">${text}</a>`;
        
        if (position === 'start') {
            navList.insertBefore(navItem, navList.firstChild);
        } else {
            navList.appendChild(navItem);
        }
        
        return navItem;
    }

    removeNavItem(selector) {
        const navItem = document.querySelector(selector);
        if (navItem && navItem.parentNode) {
            navItem.parentNode.removeChild(navItem);
        }
    }
}

// Initialize navbar component when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const navbar = new NavbarComponent();
    
    // Listen for auth state changes
    if (window.auth) {
        window.auth.onAuthChange((event, user) => {
            navbar.updateAuthState(!!user, user);
        });
    }
    
    // Make navbar globally available
    window.navbar = navbar;
});

// Add notification styles if not already present
if (!document.querySelector('#notification-styles')) {
    const style = document.createElement('style');
    style.id = 'notification-styles';
    style.textContent = `
        .notification {
            position: fixed;
            top: 100px;
            right: 20px;
            background: var(--white);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-lg);
            max-width: 400px;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform var(--transition-normal);
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification-info {
            border-left: 4px solid var(--secondary-color);
        }
        
        .notification-success {
            border-left: 4px solid var(--success);
        }
        
        .notification-warning {
            border-left: 4px solid var(--warning);
        }
        
        .notification-error {
            border-left: 4px solid var(--error);
        }
        
        .notification-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .notification-close {
            background: none;
            border: none;
            color: var(--gray);
            cursor: pointer;
            padding: var(--spacing-xs);
            margin-left: var(--spacing-md);
        }
        
        .notification-close:hover {
            color: var(--dark-gray);
        }
    `;
    document.head.appendChild(style);
}
