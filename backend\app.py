#!/usr/bin/env python3
"""
Preg and Baby Care - Flask Backend Application
Main application entry point with Flask app configuration and route registration.
"""

import os
import logging
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity
from werkzeug.security import generate_password_hash, check_password_hash
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import custom modules
from models.database import init_db, get_db_connection
from routes.auth import auth_bp
from routes.nutrition import nutrition_bp
from routes.weight import weight_bp
from routes.meal_plans import meal_plans_bp
from routes.exercise import exercise_bp
from routes.baby_care import baby_care_bp
from routes.appointments import appointments_bp
from routes.vaccinations import vaccinations_bp
from routes.schemes import schemes_bp
from routes.chatbot import chatbot_bp
from routes.admin import admin_bp
from utils.helpers import handle_error, validate_request
from services.gemini_service import GeminiService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_app():
    """Create and configure Flask application."""
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', app.config['SECRET_KEY'])
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)
    app.config['DATABASE_URL'] = os.getenv('DATABASE_URL', 'sqlite:///preg_baby_care.db')
    app.config['GEMINI_API_KEY'] = os.getenv('GEMINI_API_KEY')
    app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(__file__), 'uploads')
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
    
    # Ensure upload directory exists
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    # Initialize extensions
    CORS(app, origins=['http://localhost:3000', 'http://localhost:8000', 'http://127.0.0.1:8000'])
    jwt = JWTManager(app)
    
    # Initialize database
    with app.app_context():
        init_db()
    
    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(nutrition_bp, url_prefix='/api/nutrition')
    app.register_blueprint(weight_bp, url_prefix='/api/weight')
    app.register_blueprint(meal_plans_bp, url_prefix='/api/meal-plans')
    app.register_blueprint(exercise_bp, url_prefix='/api/exercise')
    app.register_blueprint(baby_care_bp, url_prefix='/api/baby-care')
    app.register_blueprint(appointments_bp, url_prefix='/api/appointments')
    app.register_blueprint(vaccinations_bp, url_prefix='/api/vaccinations')
    app.register_blueprint(schemes_bp, url_prefix='/api/schemes')
    app.register_blueprint(chatbot_bp, url_prefix='/api/chatbot')
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    
    # JWT error handlers
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return jsonify({'message': 'Token has expired'}), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return jsonify({'message': 'Invalid token'}), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return jsonify({'message': 'Authorization token is required'}), 401
    
    # Global error handlers
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({'message': 'Bad request', 'error': str(error)}), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return jsonify({'message': 'Unauthorized access'}), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        return jsonify({'message': 'Forbidden access'}), 403
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'message': 'Resource not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f'Internal server error: {error}')
        return jsonify({'message': 'Internal server error'}), 500
    
    # Health check endpoint
    @app.route('/api/health')
    def health_check():
        """Health check endpoint."""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0'
        })
    
    # API info endpoint
    @app.route('/api/info')
    def api_info():
        """API information endpoint."""
        return jsonify({
            'name': 'Preg and Baby Care API',
            'version': '1.0.0',
            'description': 'Backend API for pregnancy and baby care application',
            'endpoints': {
                'auth': '/api/auth',
                'nutrition': '/api/nutrition',
                'weight': '/api/weight',
                'meal-plans': '/api/meal-plans',
                'exercise': '/api/exercise',
                'baby-care': '/api/baby-care',
                'appointments': '/api/appointments',
                'vaccinations': '/api/vaccinations',
                'schemes': '/api/schemes',
                'chatbot': '/api/chatbot',
                'admin': '/api/admin'
            }
        })
    
    # Serve uploaded files
    @app.route('/uploads/<filename>')
    def uploaded_file(filename):
        """Serve uploaded files."""
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
    
    # Request logging middleware
    @app.before_request
    def log_request_info():
        """Log request information."""
        if request.endpoint != 'health_check':
            logger.info(f'{request.method} {request.url} - {request.remote_addr}')
    
    # Response headers middleware
    @app.after_request
    def after_request(response):
        """Add security headers to response."""
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        return response
    
    return app

# Create application instance
app = create_app()

if __name__ == '__main__':
    # Development server configuration
    debug_mode = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    port = int(os.getenv('PORT', 5000))
    host = os.getenv('HOST', '127.0.0.1')
    
    logger.info(f'Starting Preg and Baby Care API server on {host}:{port}')
    logger.info(f'Debug mode: {debug_mode}')
    
    app.run(
        host=host,
        port=port,
        debug=debug_mode,
        threaded=True
    )
