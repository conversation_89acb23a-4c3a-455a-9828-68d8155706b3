#!/usr/bin/env python3
"""
Initialize database with sample data for Preg and Baby Care application.
This script populates the database with realistic sample content.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from models.database import init_db, get_db, create_admin_user
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def insert_nutrition_plans():
    """Insert sample nutrition plans."""
    nutrition_plans = [
        {
            'title': 'First Trimester Nutrition Plan',
            'description': 'Essential nutrients for early pregnancy development',
            'trimester': 1,
            'calories_per_day': 2200,
            'protein_grams': 75,
            'carbs_grams': 175,
            'fat_grams': 70,
            'fiber_grams': 25,
            'iron_mg': 27,
            'calcium_mg': 1000,
            'folic_acid_mcg': 600
        },
        {
            'title': 'Second Trimester Nutrition Plan',
            'description': 'Increased nutritional needs for growing baby',
            'trimester': 2,
            'calories_per_day': 2500,
            'protein_grams': 85,
            'carbs_grams': 200,
            'fat_grams': 75,
            'fiber_grams': 28,
            'iron_mg': 27,
            'calcium_mg': 1000,
            'folic_acid_mcg': 600
        },
        {
            'title': 'Third Trimester Nutrition Plan',
            'description': 'Final stage nutrition for optimal baby development',
            'trimester': 3,
            'calories_per_day': 2800,
            'protein_grams': 95,
            'carbs_grams': 220,
            'fat_grams': 80,
            'fiber_grams': 30,
            'iron_mg': 27,
            'calcium_mg': 1200,
            'folic_acid_mcg': 600
        }
    ]
    
    with get_db() as conn:
        cursor = conn.cursor()
        for plan in nutrition_plans:
            cursor.execute("""
                INSERT INTO nutrition_plans (
                    title, description, trimester, calories_per_day,
                    protein_grams, carbs_grams, fat_grams, fiber_grams,
                    iron_mg, calcium_mg, folic_acid_mcg
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                plan['title'], plan['description'], plan['trimester'],
                plan['calories_per_day'], plan['protein_grams'], plan['carbs_grams'],
                plan['fat_grams'], plan['fiber_grams'], plan['iron_mg'],
                plan['calcium_mg'], plan['folic_acid_mcg']
            ))
        conn.commit()
    
    logger.info("Nutrition plans inserted successfully")

def insert_meal_plans():
    """Insert sample daily meal plans."""
    days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    
    sample_meals = {
        'breakfast': [
            {
                'main_dish': 'Whole grain oatmeal with berries',
                'sides': 'Greek yogurt, chopped nuts',
                'drink': 'Orange juice',
                'nutrition_highlights': 'High in fiber, folate, and protein',
                'calories': 350
            },
            {
                'main_dish': 'Scrambled eggs with spinach',
                'sides': 'Whole wheat toast, avocado',
                'drink': 'Milk',
                'nutrition_highlights': 'Rich in protein, iron, and healthy fats',
                'calories': 400
            }
        ],
        'lunch': [
            {
                'main_dish': 'Grilled chicken salad',
                'sides': 'Mixed greens, quinoa, vegetables',
                'drink': 'Water with lemon',
                'nutrition_highlights': 'Lean protein, complex carbs, vitamins',
                'calories': 450
            },
            {
                'main_dish': 'Lentil soup',
                'sides': 'Whole grain bread, side salad',
                'drink': 'Herbal tea',
                'nutrition_highlights': 'Plant protein, fiber, iron',
                'calories': 400
            }
        ],
        'dinner': [
            {
                'main_dish': 'Baked salmon with sweet potato',
                'sides': 'Steamed broccoli, brown rice',
                'drink': 'Water',
                'nutrition_highlights': 'Omega-3 fatty acids, vitamin A, fiber',
                'calories': 550
            },
            {
                'main_dish': 'Lean beef stir-fry',
                'sides': 'Mixed vegetables, quinoa',
                'drink': 'Milk',
                'nutrition_highlights': 'Iron, protein, vitamins',
                'calories': 500
            }
        ]
    }
    
    with get_db() as conn:
        cursor = conn.cursor()
        
        for trimester in [1, 2, 3]:
            for day in days:
                for meal_type, meals in sample_meals.items():
                    meal = meals[hash(day + meal_type) % len(meals)]
                    
                    cursor.execute("""
                        INSERT INTO daily_meal_plans (
                            day_of_week, trimester, cuisine_type, meal_type,
                            main_dish, sides, drink, nutrition_highlights, calories
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        day, trimester, 'standard', meal_type,
                        meal['main_dish'], meal['sides'], meal['drink'],
                        meal['nutrition_highlights'], meal['calories']
                    ))
        
        conn.commit()
    
    logger.info("Meal plans inserted successfully")

def insert_exercise_guides():
    """Insert sample exercise guides."""
    exercises = [
        {
            'title': 'Prenatal Yoga Flow',
            'description': 'Gentle yoga sequence for pregnancy',
            'category': 'Prenatal Yoga',
            'difficulty_level': 'Beginner',
            'duration_minutes': 30,
            'trimester_safe': 'all',
            'instructions': 'Start with gentle warm-up movements...',
            'benefits': 'Improves flexibility, reduces stress, strengthens core',
            'precautions': 'Avoid deep twists and backbends'
        },
        {
            'title': 'Walking for Pregnancy',
            'description': 'Safe cardiovascular exercise during pregnancy',
            'category': 'Cardio',
            'difficulty_level': 'Beginner',
            'duration_minutes': 20,
            'trimester_safe': 'all',
            'instructions': 'Start with 5-minute warm-up walk...',
            'benefits': 'Improves cardiovascular health, maintains fitness',
            'precautions': 'Stay hydrated, avoid overheating'
        },
        {
            'title': 'Pelvic Floor Exercises',
            'description': 'Strengthen pelvic floor muscles',
            'category': 'Pelvic Floor',
            'difficulty_level': 'Beginner',
            'duration_minutes': 15,
            'trimester_safe': 'all',
            'instructions': 'Contract pelvic floor muscles for 5 seconds...',
            'benefits': 'Prevents incontinence, aids in delivery',
            'precautions': 'Don\'t hold breath during exercises'
        }
    ]
    
    with get_db() as conn:
        cursor = conn.cursor()
        for exercise in exercises:
            cursor.execute("""
                INSERT INTO exercise_guides (
                    title, description, category, difficulty_level,
                    duration_minutes, trimester_safe, instructions,
                    benefits, precautions
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                exercise['title'], exercise['description'], exercise['category'],
                exercise['difficulty_level'], exercise['duration_minutes'],
                exercise['trimester_safe'], exercise['instructions'],
                exercise['benefits'], exercise['precautions']
            ))
        conn.commit()
    
    logger.info("Exercise guides inserted successfully")

def insert_baby_care_content():
    """Insert sample baby care content."""
    content = [
        {
            'title': 'Newborn Feeding Guidelines',
            'content': 'Newborns need to feed every 2-3 hours...',
            'category': 'Feeding',
            'age_group': 'newborn',
            'tags': 'breastfeeding,formula,schedule'
        },
        {
            'title': 'Baby Sleep Patterns',
            'content': 'Newborns sleep 14-17 hours per day...',
            'category': 'Sleep',
            'age_group': 'newborn',
            'tags': 'sleep,schedule,safety'
        },
        {
            'title': 'Infant Development Milestones',
            'content': 'By 2 months, babies typically...',
            'category': 'Development',
            'age_group': 'infant',
            'tags': 'milestones,development,growth'
        }
    ]
    
    with get_db() as conn:
        cursor = conn.cursor()
        for item in content:
            cursor.execute("""
                INSERT INTO baby_care_content (
                    title, content, category, age_group, tags
                ) VALUES (?, ?, ?, ?, ?)
            """, (
                item['title'], item['content'], item['category'],
                item['age_group'], item['tags']
            ))
        conn.commit()
    
    logger.info("Baby care content inserted successfully")

def insert_vaccinations():
    """Insert sample vaccination schedule."""
    vaccinations = [
        {
            'name': 'Hepatitis B',
            'description': 'Protects against hepatitis B virus',
            'age_months': 0,
            'is_required': True,
            'side_effects': 'Mild fever, soreness at injection site'
        },
        {
            'name': 'DTaP (Diphtheria, Tetanus, Pertussis)',
            'description': 'Protects against three serious diseases',
            'age_months': 2,
            'is_required': True,
            'side_effects': 'Mild fever, fussiness, drowsiness'
        },
        {
            'name': 'MMR (Measles, Mumps, Rubella)',
            'description': 'Protects against measles, mumps, and rubella',
            'age_months': 12,
            'is_required': True,
            'side_effects': 'Mild fever, rash'
        }
    ]
    
    with get_db() as conn:
        cursor = conn.cursor()
        for vaccine in vaccinations:
            cursor.execute("""
                INSERT INTO vaccinations (
                    name, description, age_months, is_required, side_effects
                ) VALUES (?, ?, ?, ?, ?)
            """, (
                vaccine['name'], vaccine['description'], vaccine['age_months'],
                vaccine['is_required'], vaccine['side_effects']
            ))
        conn.commit()
    
    logger.info("Vaccinations inserted successfully")

def insert_government_schemes():
    """Insert sample government schemes."""
    schemes = [
        {
            'name': 'Janani Suraksha Yojana (JSY)',
            'description': 'Cash assistance for institutional delivery',
            'eligibility_criteria': 'Pregnant women below poverty line',
            'benefits': 'Cash incentive for hospital delivery',
            'application_process': 'Apply at nearest health center',
            'required_documents': 'BPL card, pregnancy certificate',
            'contact_info': 'Contact local ASHA worker'
        },
        {
            'name': 'Pradhan Mantri Matru Vandana Yojana',
            'description': 'Maternity benefit scheme',
            'eligibility_criteria': 'Pregnant and lactating mothers',
            'benefits': 'Rs. 5000 in three installments',
            'application_process': 'Apply online or at Anganwadi center',
            'required_documents': 'Aadhaar card, bank account details',
            'contact_info': 'Visit nearest Anganwadi center'
        }
    ]
    
    with get_db() as conn:
        cursor = conn.cursor()
        for scheme in schemes:
            cursor.execute("""
                INSERT INTO government_schemes (
                    name, description, eligibility_criteria, benefits,
                    application_process, required_documents, contact_info
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                scheme['name'], scheme['description'], scheme['eligibility_criteria'],
                scheme['benefits'], scheme['application_process'],
                scheme['required_documents'], scheme['contact_info']
            ))
        conn.commit()
    
    logger.info("Government schemes inserted successfully")

def main():
    """Initialize database with sample data."""
    logger.info("Starting database initialization...")
    
    # Initialize database schema
    init_db()
    
    # Create admin user
    create_admin_user()
    
    # Insert sample data
    insert_nutrition_plans()
    insert_meal_plans()
    insert_exercise_guides()
    insert_baby_care_content()
    insert_vaccinations()
    insert_government_schemes()
    
    logger.info("Database initialization completed successfully!")

if __name__ == "__main__":
    main()
