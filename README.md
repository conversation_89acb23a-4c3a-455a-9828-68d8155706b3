# Preg and Baby Care - HTML/CSS/JS + Python Version

This is a complete recreation of the React-based pregnancy and baby care application using vanilla HTML, CSS, JavaScript for the frontend and Python Flask for the backend.

## 🚀 Quick Start

### Option 1: Automatic Setup (Recommended)

```bash
# Clone or download the project
cd preg-baby-care-html

# Run the startup script (handles everything automatically)
python start.py
```

### Option 2: Manual Setup

```bash
# 1. Install Python dependencies
pip install -r requirements.txt

# 2. Setup environment variables
cp backend/.env.example backend/.env
# Edit backend/.env with your API keys

# 3. Initialize database
cd backend
python database/init_sample_data.py

# 4. Start backend server
python app.py

# 5. In another terminal, start frontend server
cd frontend
python -m http.server 8000

# 6. Open http://localhost:8000 in your browser
```

## 📁 Project Structure

```
preg-baby-care-html/
├── 📄 start.py              # Automatic startup script
├── 📄 requirements.txt      # Python dependencies
├── 📄 README.md            # This file
├── 📁 frontend/            # Frontend HTML/CSS/JS
│   ├── 📄 index.html       # Main entry point
│   ├── 📁 css/             # Stylesheets
│   │   ├── main.css        # Core styles and variables
│   │   ├── components.css  # Component-specific styles
│   │   └── responsive.css  # Mobile-responsive styles
│   ├── 📁 js/              # JavaScript modules
│   │   ├── utils/          # Utility functions
│   │   │   ├── config.js   # App configuration
│   │   │   ├── api.js      # API client
│   │   │   └── auth.js     # Authentication manager
│   │   ├── components/     # Reusable components
│   │   │   ├── navbar.js   # Navigation component
│   │   │   └── chatbot.js  # AI chatbot component
│   │   └── main.js         # Main application logic
│   └── 📁 pages/           # HTML pages
│       ├── login.html      # User login
│       ├── signup.html     # User registration
│       └── pregnancy-care.html # Pregnancy guide
├── 📁 backend/             # Python Flask backend
│   ├── 📄 app.py           # Main Flask application
│   ├── 📄 .env.example     # Environment variables template
│   ├── 📁 models/          # Database models
│   │   └── database.py     # Database setup and schema
│   ├── 📁 routes/          # API route handlers
│   │   ├── auth.py         # Authentication routes
│   │   ├── nutrition.py    # Nutrition management
│   │   ├── weight.py       # Weight tracking
│   │   ├── meal_plans.py   # Daily meal plans
│   │   ├── chatbot.py      # AI chatbot integration
│   │   └── admin.py        # Admin functionality
│   ├── 📁 services/        # Business logic
│   │   └── gemini_service.py # Google Gemini AI integration
│   ├── 📁 utils/           # Utility functions
│   │   └── helpers.py      # Common helper functions
│   └── 📁 database/        # Database files
│       └── init_sample_data.py # Sample data initialization
```

## ✨ Features

### 🤰 Pregnancy Care

- **Trimester Guides**: Comprehensive information for each pregnancy stage
- **Nutrition Plans**: Personalized meal recommendations
- **Weight Tracker**: Visual weight gain monitoring with charts
- **Exercise Guides**: Safe pregnancy workouts and activities
- **Appointment Scheduling**: Manage prenatal appointments

### 👶 Baby Care

- **Development Milestones**: Track baby's growth and development
- **Feeding Guides**: Breastfeeding and formula feeding information
- **Sleep Patterns**: Baby sleep schedules and tips
- **Vaccination Schedule**: Track immunizations and health records
- **Activities**: Age-appropriate activities and games

### 🤖 AI Assistant

- **Gemini AI Integration**: Intelligent pregnancy and baby care assistance
- **24/7 Support**: Get answers to questions anytime
- **Personalized Responses**: Context-aware recommendations
- **Emergency Guidance**: Urgent care information and alerts

### 👨‍💼 Admin Panel

- **Content Management**: Update nutrition plans, exercises, and guides
- **User Management**: Monitor user accounts and activity
- **Analytics Dashboard**: Track application usage and engagement

## 🛠️ Technology Stack

### Frontend

- **HTML5**: Semantic markup and accessibility
- **CSS3**: Modern styling with Flexbox/Grid, custom properties
- **Vanilla JavaScript (ES6+)**: No frameworks, pure JavaScript
- **Chart.js**: Data visualization for weight tracking
- **Font Awesome**: Icon library

### Backend

- **Python 3.8+**: Modern Python with type hints
- **Flask**: Lightweight web framework
- **SQLite**: Embedded database for easy deployment
- **JWT**: Secure authentication tokens
- **Google Gemini AI**: Advanced AI assistance
- **Flask-CORS**: Cross-origin resource sharing

## 🔧 Configuration

### Environment Variables

Create `backend/.env` file:

```env
# Security
SECRET_KEY=your_super_secret_key_change_in_production
JWT_SECRET_KEY=your_jwt_secret_key

# Google Gemini AI
GEMINI_API_KEY=your_gemini_api_key_here

# Database
DATABASE_URL=sqlite:///preg_baby_care.db

# Development
FLASK_ENV=development
FLASK_DEBUG=True
HOST=127.0.0.1
PORT=5000
```

### API Endpoints

#### Authentication

- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `POST /api/auth/change-password` - Change password

#### Nutrition & Meal Plans

- `GET /api/nutrition/plans` - Get nutrition plans
- `GET /api/meal-plans` - Get meal plans
- `GET /api/meal-plans/day` - Get daily meal plan
- `POST /api/meal-plans` - Create meal plan (admin)

#### Health Tracking

- `GET /api/weight/entries` - Get weight entries
- `POST /api/weight/entries` - Add weight entry
- `GET /api/weight/stats` - Get weight statistics

#### AI Chatbot

- `POST /api/chatbot/message` - Send message to AI
- `GET /api/chatbot/history` - Get conversation history
- `DELETE /api/chatbot/history` - Clear conversation history

#### Admin

- `GET /api/admin/dashboard` - Admin dashboard data
- `GET /api/admin/users` - Manage users
- `GET /api/admin/analytics` - Usage analytics

## 👥 Demo Accounts

### Admin Account

- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Access**: Full admin privileges, content management

### Test User Account

- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Access**: Standard user features

## 🚀 Deployment

### Local Development

```bash
# Start both servers automatically
python start.py

# Or manually:
# Terminal 1 - Backend
cd backend && python app.py

# Terminal 2 - Frontend
cd frontend && python -m http.server 8000
```

### Production Deployment

1. **Backend**: Deploy Flask app to services like Heroku, DigitalOcean, or AWS
2. **Frontend**: Serve static files via Nginx, Apache, or CDN
3. **Database**: Migrate from SQLite to PostgreSQL/MySQL for production
4. **Environment**: Set production environment variables

## 📱 Browser Support

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational purposes. Feel free to use and modify for learning.

## 🆘 Support

For issues and questions:

1. Check the console for error messages
2. Verify all dependencies are installed
3. Ensure environment variables are set correctly
4. Check that both servers are running

## 🔮 Future Enhancements

- [ ] Progressive Web App (PWA) support
- [ ] Push notifications for appointments
- [ ] Multi-language support
- [ ] Advanced analytics dashboard
- [ ] Integration with wearable devices
- [ ] Telemedicine video consultations
