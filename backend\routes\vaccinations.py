"""
Vaccinations routes for managing vaccination schedules and records.
"""

import logging
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

from models.database import get_db
from utils.helpers import handle_error, convert_rows_to_dicts

logger = logging.getLogger(__name__)
vaccinations_bp = Blueprint('vaccinations', __name__)

@vaccinations_bp.route('/schedule', methods=['GET'])
def get_vaccination_schedule():
    """Get vaccination schedule."""
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM vaccinations
                WHERE is_active = 1
                ORDER BY age_months
            """)
            
            vaccinations = convert_rows_to_dicts(cursor.fetchall())
            
            return jsonify({'vaccinations': vaccinations}), 200
            
    except Exception as e:
        logger.error(f"Get vaccination schedule error: {str(e)}")
        return handle_error(e)

@vaccinations_bp.route('/records', methods=['GET'])
@jwt_required()
def get_vaccination_records():
    """Get vaccination records for current user."""
    try:
        user_id = get_jwt_identity()
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT vr.*, v.name, v.description, v.age_months
                FROM user_vaccination_records vr
                JOIN vaccinations v ON vr.vaccination_id = v.id
                WHERE vr.user_id = ?
                ORDER BY vr.date_administered DESC
            """, (user_id,))
            
            records = convert_rows_to_dicts(cursor.fetchall())
            
            return jsonify({'records': records}), 200
            
    except Exception as e:
        logger.error(f"Get vaccination records error: {str(e)}")
        return handle_error(e)
