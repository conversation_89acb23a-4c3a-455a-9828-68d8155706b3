"""
Appointments routes for managing doctor appointments and scheduling.
"""

import logging
from datetime import datetime, date
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

from models.database import get_db
from utils.helpers import handle_error, convert_rows_to_dicts

logger = logging.getLogger(__name__)
appointments_bp = Blueprint('appointments', __name__)

@appointments_bp.route('', methods=['GET'])
@jwt_required()
def get_appointments():
    """Get appointments for current user."""
    try:
        user_id = get_jwt_identity()
        status = request.args.get('status')
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT * FROM appointments
                WHERE user_id = ?
            """
            params = [user_id]
            
            if status:
                query += " AND status = ?"
                params.append(status)
            
            query += " ORDER BY appointment_date DESC, appointment_time DESC"
            
            cursor.execute(query, params)
            appointments = convert_rows_to_dicts(cursor.fetchall())
            
            return jsonify({'appointments': appointments}), 200
            
    except Exception as e:
        logger.error(f"Get appointments error: {str(e)}")
        return handle_error(e)

@appointments_bp.route('', methods=['POST'])
@jwt_required()
def create_appointment():
    """Create new appointment."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        required_fields = ['doctor_name', 'appointment_type', 'appointment_date', 'appointment_time']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'message': f'{field} is required'}), 400
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO appointments (
                    user_id, doctor_name, appointment_type, appointment_date,
                    appointment_time, duration_minutes, location, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                user_id,
                data['doctor_name'],
                data['appointment_type'],
                data['appointment_date'],
                data['appointment_time'],
                data.get('duration_minutes', 30),
                data.get('location', ''),
                data.get('notes', '')
            ))
            
            appointment_id = cursor.lastrowid
            conn.commit()
            
            logger.info(f"Appointment created: {appointment_id} for user: {user_id}")
            
            return jsonify({
                'message': 'Appointment created successfully',
                'appointment_id': appointment_id
            }), 201
            
    except Exception as e:
        logger.error(f"Create appointment error: {str(e)}")
        return handle_error(e)
