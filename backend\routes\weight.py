"""
Weight tracking routes for pregnancy weight management.
"""

import logging
from datetime import datetime, date
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

from models.database import get_db
from utils.helpers import handle_error, convert_rows_to_dicts, calculate_bmi, calculate_recommended_weight_gain

logger = logging.getLogger(__name__)
weight_bp = Blueprint('weight', __name__)

@weight_bp.route('/entries', methods=['GET'])
@jwt_required()
def get_weight_entries():
    """Get weight entries for current user."""
    try:
        user_id = get_jwt_identity()
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM weight_entries
                WHERE user_id = ?
                ORDER BY date_recorded DESC, week DESC
            """, (user_id,))
            
            entries = convert_rows_to_dicts(cursor.fetchall())
            
            return jsonify({'entries': entries}), 200
            
    except Exception as e:
        logger.error(f"Get weight entries error: {str(e)}")
        return handle_error(e)

@weight_bp.route('/entries', methods=['POST'])
@jwt_required()
def add_weight_entry():
    """Add new weight entry."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        # Validate required fields
        if not data.get('weight') or not data.get('week'):
            return jsonify({'message': 'Weight and week are required'}), 400
        
        weight = float(data['weight'])
        week = int(data['week'])
        date_recorded = data.get('date_recorded', date.today().isoformat())
        notes = data.get('notes', '')
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO weight_entries (user_id, weight, week, date_recorded, notes)
                VALUES (?, ?, ?, ?, ?)
            """, (user_id, weight, week, date_recorded, notes))
            
            entry_id = cursor.lastrowid
            conn.commit()
            
            logger.info(f"Weight entry added for user {user_id}: {weight}kg at week {week}")
            
            return jsonify({
                'message': 'Weight entry added successfully',
                'entry_id': entry_id
            }), 201
            
    except Exception as e:
        logger.error(f"Add weight entry error: {str(e)}")
        return handle_error(e)

@weight_bp.route('/stats', methods=['GET'])
@jwt_required()
def get_weight_stats():
    """Get weight statistics and recommendations."""
    try:
        user_id = get_jwt_identity()
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get user profile
            cursor.execute("""
                SELECT pre_pregnancy_weight, height, current_week
                FROM user_profiles
                WHERE user_id = ?
            """, (user_id,))
            
            profile = cursor.fetchone()
            if not profile:
                return jsonify({'message': 'User profile not found'}), 404
            
            profile = dict(profile)
            
            # Get weight entries
            cursor.execute("""
                SELECT weight, week, date_recorded
                FROM weight_entries
                WHERE user_id = ?
                ORDER BY week
            """, (user_id,))
            
            entries = convert_rows_to_dicts(cursor.fetchall())
            
            # Calculate statistics
            stats = calculate_weight_stats(profile, entries)
            
            return jsonify({'stats': stats}), 200
            
    except Exception as e:
        logger.error(f"Get weight stats error: {str(e)}")
        return handle_error(e)

def calculate_weight_stats(profile, entries):
    """Calculate weight statistics and recommendations."""
    stats = {
        'pre_pregnancy_weight': profile.get('pre_pregnancy_weight'),
        'current_week': profile.get('current_week'),
        'total_entries': len(entries),
        'weight_gain': 0,
        'recommended_gain': None,
        'bmi_category': None,
        'chart_data': []
    }
    
    if not entries or not profile.get('pre_pregnancy_weight'):
        return stats
    
    pre_weight = profile['pre_pregnancy_weight']
    height = profile.get('height')
    
    # Calculate BMI if height is available
    if height:
        bmi = calculate_bmi(pre_weight, height)
        stats['pre_pregnancy_bmi'] = bmi
        stats['bmi_category'] = get_bmi_category(bmi)
        stats['recommended_gain'] = calculate_recommended_weight_gain(bmi)
    
    # Calculate current weight gain
    if entries:
        latest_weight = entries[-1]['weight']
        stats['current_weight'] = latest_weight
        stats['weight_gain'] = round(latest_weight - pre_weight, 1)
    
    # Prepare chart data
    stats['chart_data'] = [
        {
            'week': entry['week'],
            'weight': entry['weight'],
            'weight_gain': round(entry['weight'] - pre_weight, 1),
            'date': entry['date_recorded']
        }
        for entry in entries
    ]
    
    return stats

def get_bmi_category(bmi):
    """Get BMI category."""
    if bmi < 18.5:
        return 'Underweight'
    elif bmi < 25:
        return 'Normal weight'
    elif bmi < 30:
        return 'Overweight'
    else:
        return 'Obese'
