<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Preg and Baby Care</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/responsive.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            padding: var(--spacing-lg);
        }
        
        .auth-card {
            background: var(--white);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-2xl);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .auth-logo {
            color: var(--primary-color);
            font-size: var(--font-size-3xl);
            margin-bottom: var(--spacing-sm);
        }
        
        .auth-title {
            color: var(--dark-gray);
            margin-bottom: var(--spacing-lg);
        }
        
        .auth-subtitle {
            color: var(--gray);
            margin-bottom: var(--spacing-2xl);
        }
        
        .auth-form {
            text-align: left;
        }
        
        .auth-links {
            text-align: center;
            margin-top: var(--spacing-lg);
        }
        
        .auth-links a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .auth-links a:hover {
            text-decoration: underline;
        }
        
        .divider {
            margin: var(--spacing-lg) 0;
            text-align: center;
            position: relative;
            color: var(--gray);
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
        }
        
        .divider span {
            background: var(--white);
            padding: 0 var(--spacing-md);
        }
        
        .demo-credentials {
            background: var(--light-gray);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
            font-size: var(--font-size-sm);
        }
        
        .demo-credentials h4 {
            margin-bottom: var(--spacing-sm);
            color: var(--dark-gray);
        }
        
        .demo-credentials p {
            margin-bottom: var(--spacing-xs);
            color: var(--gray);
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-logo">
                <i class="fas fa-baby"></i>
            </div>
            <h1 class="auth-title">Welcome Back</h1>
            <p class="auth-subtitle">Sign in to your Preg and Baby Care account</p>
            
            <!-- Demo Credentials -->
            <div class="demo-credentials">
                <h4>Demo Credentials</h4>
                <p><strong>Admin:</strong> <EMAIL> / admin123</p>
                <p><strong>User:</strong> <EMAIL> / password123</p>
            </div>
            
            <!-- Alert Messages -->
            <div id="alert-container"></div>
            
            <!-- Login Form -->
            <form class="auth-form" id="login-form">
                <div class="form-group">
                    <label for="email" class="form-label">
                        <i class="fas fa-envelope"></i> Email Address
                    </label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-input" 
                        placeholder="Enter your email"
                        required
                    >
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i> Password
                    </label>
                    <div style="position: relative;">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input" 
                            placeholder="Enter your password"
                            required
                        >
                        <button 
                            type="button" 
                            id="toggle-password"
                            style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--gray); cursor: pointer;"
                        >
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-group" style="display: flex; justify-content: space-between; align-items: center;">
                    <label style="display: flex; align-items: center; margin-bottom: 0;">
                        <input type="checkbox" id="remember-me" style="margin-right: var(--spacing-sm);">
                        Remember me
                    </label>
                    <a href="forgot-password.html" style="color: var(--primary-color); text-decoration: none; font-size: var(--font-size-sm);">
                        Forgot password?
                    </a>
                </div>
                
                <button type="submit" class="btn btn-primary" style="width: 100%; margin-top: var(--spacing-lg);" id="login-btn">
                    <span id="login-text">Sign In</span>
                    <span id="login-spinner" class="spinner" style="display: none;"></span>
                </button>
            </form>
            
            <div class="divider">
                <span>or</span>
            </div>
            
            <div class="auth-links">
                <p>Don't have an account? <a href="signup.html">Sign up here</a></p>
                <p><a href="../index.html">← Back to Home</a></p>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="../js/utils/config.js"></script>
    <script src="../js/utils/api.js"></script>
    <script src="../js/utils/auth.js"></script>
    
    <script>
        // Login form handling
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const loginBtn = document.getElementById('login-btn');
            const loginText = document.getElementById('login-text');
            const loginSpinner = document.getElementById('login-spinner');
            const togglePassword = document.getElementById('toggle-password');
            const passwordInput = document.getElementById('password');
            const alertContainer = document.getElementById('alert-container');
            
            // Toggle password visibility
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
            
            // Handle form submission
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = new FormData(loginForm);
                const credentials = {
                    email: formData.get('email'),
                    password: formData.get('password')
                };
                
                // Show loading state
                loginBtn.disabled = true;
                loginText.style.display = 'none';
                loginSpinner.style.display = 'inline-block';
                
                try {
                    const response = await API.auth.login(credentials);
                    
                    // Store authentication data
                    localStorage.setItem(CONFIG.AUTH.TOKEN_KEY, response.access_token);
                    localStorage.setItem(CONFIG.AUTH.USER_KEY, JSON.stringify(response.user));
                    
                    // Show success message
                    showAlert('Login successful! Redirecting...', 'success');
                    
                    // Redirect based on user role
                    setTimeout(() => {
                        if (response.user.is_admin) {
                            window.location.href = 'admin-dashboard.html';
                        } else {
                            window.location.href = '../index.html';
                        }
                    }, 1500);
                    
                } catch (error) {
                    console.error('Login error:', error);
                    showAlert(error.message || 'Login failed. Please try again.', 'error');
                } finally {
                    // Reset loading state
                    loginBtn.disabled = false;
                    loginText.style.display = 'inline';
                    loginSpinner.style.display = 'none';
                }
            });
            
            // Auto-fill demo credentials
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'd') {
                    e.preventDefault();
                    document.getElementById('email').value = '<EMAIL>';
                    document.getElementById('password').value = 'admin123';
                }
            });
            
            function showAlert(message, type) {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type}`;
                alertDiv.textContent = message;
                
                alertContainer.innerHTML = '';
                alertContainer.appendChild(alertDiv);
                
                // Auto-remove after 5 seconds
                setTimeout(() => {
                    alertDiv.remove();
                }, 5000);
            }
            
            // Check if user is already logged in
            const token = localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
            if (token) {
                window.location.href = '../index.html';
            }
        });
    </script>
</body>
</html>
