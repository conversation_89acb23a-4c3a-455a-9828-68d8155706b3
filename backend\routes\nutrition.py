"""
Nutrition routes for managing pregnancy nutrition plans and recommendations.
"""

import logging
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

from models.database import get_db
from utils.helpers import handle_error, convert_rows_to_dicts, paginate_results, require_admin

logger = logging.getLogger(__name__)
nutrition_bp = Blueprint('nutrition', __name__)

@nutrition_bp.route('/plans', methods=['GET'])
def get_nutrition_plans():
    """Get all nutrition plans with optional filtering."""
    try:
        trimester = request.args.get('trimester', type=int)
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Build query with optional filtering
            query = "SELECT * FROM nutrition_plans WHERE is_active = 1"
            params = []
            
            if trimester:
                query += " AND trimester = ?"
                params.append(trimester)
            
            query += " ORDER BY trimester, created_at DESC"
            
            cursor.execute(query, params)
            plans = convert_rows_to_dicts(cursor.fetchall())
            
            # Paginate results
            paginated = paginate_results(plans, page, per_page)
            
            return jsonify(paginated), 200
            
    except Exception as e:
        logger.error(f"Get nutrition plans error: {str(e)}")
        return handle_error(e)

@nutrition_bp.route('/plans/<int:plan_id>', methods=['GET'])
def get_nutrition_plan(plan_id):
    """Get specific nutrition plan by ID."""
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM nutrition_plans 
                WHERE id = ? AND is_active = 1
            """, (plan_id,))
            
            plan = cursor.fetchone()
            
            if not plan:
                return jsonify({'message': 'Nutrition plan not found'}), 404
            
            return jsonify({'plan': dict(plan)}), 200
            
    except Exception as e:
        logger.error(f"Get nutrition plan error: {str(e)}")
        return handle_error(e)

@nutrition_bp.route('/plans', methods=['POST'])
@jwt_required()
@require_admin
def create_nutrition_plan():
    """Create new nutrition plan (admin only)."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['title', 'trimester', 'calories_per_day']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'message': f'{field} is required'}), 400
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO nutrition_plans (
                    title, description, trimester, calories_per_day,
                    protein_grams, carbs_grams, fat_grams, fiber_grams,
                    iron_mg, calcium_mg, folic_acid_mcg
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['title'],
                data.get('description', ''),
                data['trimester'],
                data['calories_per_day'],
                data.get('protein_grams'),
                data.get('carbs_grams'),
                data.get('fat_grams'),
                data.get('fiber_grams'),
                data.get('iron_mg'),
                data.get('calcium_mg'),
                data.get('folic_acid_mcg')
            ))
            
            plan_id = cursor.lastrowid
            conn.commit()
            
            logger.info(f"Nutrition plan created: {plan_id}")
            
            return jsonify({
                'message': 'Nutrition plan created successfully',
                'plan_id': plan_id
            }), 201
            
    except Exception as e:
        logger.error(f"Create nutrition plan error: {str(e)}")
        return handle_error(e)

@nutrition_bp.route('/plans/<int:plan_id>', methods=['PUT'])
@jwt_required()
@require_admin
def update_nutrition_plan(plan_id):
    """Update nutrition plan (admin only)."""
    try:
        data = request.get_json()
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check if plan exists
            cursor.execute("SELECT id FROM nutrition_plans WHERE id = ?", (plan_id,))
            if not cursor.fetchone():
                return jsonify({'message': 'Nutrition plan not found'}), 404
            
            # Build update query
            update_fields = []
            values = []
            
            updatable_fields = [
                'title', 'description', 'trimester', 'calories_per_day',
                'protein_grams', 'carbs_grams', 'fat_grams', 'fiber_grams',
                'iron_mg', 'calcium_mg', 'folic_acid_mcg'
            ]
            
            for field in updatable_fields:
                if field in data:
                    update_fields.append(f"{field} = ?")
                    values.append(data[field])
            
            if not update_fields:
                return jsonify({'message': 'No fields to update'}), 400
            
            values.append(plan_id)
            
            cursor.execute(f"""
                UPDATE nutrition_plans 
                SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, values)
            
            conn.commit()
            
            logger.info(f"Nutrition plan updated: {plan_id}")
            
            return jsonify({'message': 'Nutrition plan updated successfully'}), 200
            
    except Exception as e:
        logger.error(f"Update nutrition plan error: {str(e)}")
        return handle_error(e)

@nutrition_bp.route('/plans/<int:plan_id>', methods=['DELETE'])
@jwt_required()
@require_admin
def delete_nutrition_plan(plan_id):
    """Delete nutrition plan (admin only)."""
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check if plan exists
            cursor.execute("SELECT id FROM nutrition_plans WHERE id = ?", (plan_id,))
            if not cursor.fetchone():
                return jsonify({'message': 'Nutrition plan not found'}), 404
            
            # Soft delete by setting is_active to False
            cursor.execute("""
                UPDATE nutrition_plans 
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (plan_id,))
            
            conn.commit()
            
            logger.info(f"Nutrition plan deleted: {plan_id}")
            
            return jsonify({'message': 'Nutrition plan deleted successfully'}), 200
            
    except Exception as e:
        logger.error(f"Delete nutrition plan error: {str(e)}")
        return handle_error(e)

@nutrition_bp.route('/recommendations', methods=['GET'])
@jwt_required()
def get_nutrition_recommendations():
    """Get personalized nutrition recommendations for current user."""
    try:
        user_id = get_jwt_identity()
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Get user profile
            cursor.execute("""
                SELECT p.current_week, p.pre_pregnancy_weight, p.height,
                       p.medical_conditions, p.allergies
                FROM user_profiles p
                WHERE p.user_id = ?
            """, (user_id,))
            
            profile = cursor.fetchone()
            
            if not profile:
                return jsonify({'message': 'User profile not found'}), 404
            
            profile = dict(profile)
            
            # Determine trimester based on current week
            current_week = profile.get('current_week', 0)
            if current_week <= 12:
                trimester = 1
            elif current_week <= 27:
                trimester = 2
            else:
                trimester = 3
            
            # Get nutrition plan for current trimester
            cursor.execute("""
                SELECT * FROM nutrition_plans 
                WHERE trimester = ? AND is_active = 1
                ORDER BY created_at DESC
                LIMIT 1
            """, (trimester,))
            
            plan = cursor.fetchone()
            
            if not plan:
                return jsonify({'message': 'No nutrition plan found for current trimester'}), 404
            
            plan = dict(plan)
            
            # Calculate personalized recommendations
            recommendations = calculate_personalized_nutrition(profile, plan)
            
            return jsonify({
                'trimester': trimester,
                'current_week': current_week,
                'plan': plan,
                'recommendations': recommendations
            }), 200
            
    except Exception as e:
        logger.error(f"Get nutrition recommendations error: {str(e)}")
        return handle_error(e)

def calculate_personalized_nutrition(profile, base_plan):
    """Calculate personalized nutrition recommendations based on user profile."""
    recommendations = {
        'calories': base_plan.get('calories_per_day', 2200),
        'protein': base_plan.get('protein_grams', 75),
        'carbs': base_plan.get('carbs_grams', 175),
        'fat': base_plan.get('fat_grams', 70),
        'fiber': base_plan.get('fiber_grams', 25),
        'iron': base_plan.get('iron_mg', 27),
        'calcium': base_plan.get('calcium_mg', 1000),
        'folic_acid': base_plan.get('folic_acid_mcg', 600)
    }
    
    # Adjust based on pre-pregnancy weight and height
    if profile.get('pre_pregnancy_weight') and profile.get('height'):
        weight = profile['pre_pregnancy_weight']
        height = profile['height'] / 100  # Convert cm to m
        bmi = weight / (height ** 2)
        
        # Adjust calories based on BMI
        if bmi < 18.5:  # Underweight
            recommendations['calories'] += 200
        elif bmi > 30:  # Obese
            recommendations['calories'] -= 100
    
    # Add special considerations based on medical conditions
    medical_conditions = profile.get('medical_conditions', '').lower()
    if 'diabetes' in medical_conditions:
        recommendations['special_notes'] = ['Monitor carbohydrate intake', 'Focus on complex carbs']
    elif 'anemia' in medical_conditions:
        recommendations['iron'] += 10
        recommendations['special_notes'] = ['Increase iron-rich foods', 'Take iron supplements as prescribed']
    
    # Add allergy considerations
    allergies = profile.get('allergies', '').lower()
    if allergies:
        recommendations['allergies_note'] = f'Avoid: {allergies}'
    
    return recommendations
