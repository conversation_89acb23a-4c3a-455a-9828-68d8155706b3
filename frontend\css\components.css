/* Navigation Bar */
.navbar {
  background: var(--white);
  box-shadow: var(--shadow-sm);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all var(--transition-normal);
}

.navbar.scrolled {
  box-shadow: var(--shadow-md);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.nav-logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--primary-color);
  font-size: var(--font-size-xl);
  font-weight: 700;
}

.nav-logo i {
  margin-right: var(--spacing-sm);
  font-size: var(--font-size-2xl);
}

.nav-menu {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
  margin: 0 var(--spacing-sm);
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  text-decoration: none;
  color: var(--dark-gray);
  font-weight: 500;
  transition: color var(--transition-normal);
  border-radius: var(--radius-md);
}

.nav-link:hover {
  color: var(--primary-color);
  background: var(--primary-light);
}

.nav-link.active {
  color: var(--primary-color);
  background: var(--primary-light);
}

/* Dropdown Menu */
.dropdown {
  position: relative;
}

.dropdown-toggle i {
  margin-left: var(--spacing-xs);
  font-size: var(--font-size-sm);
  transition: transform var(--transition-normal);
}

.dropdown:hover .dropdown-toggle i {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--white);
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) 0;
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-normal);
  list-style: none;
  z-index: 1001;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu li {
  margin: 0;
}

.dropdown-menu a {
  display: block;
  padding: var(--spacing-sm) var(--spacing-lg);
  text-decoration: none;
  color: var(--dark-gray);
  transition: all var(--transition-normal);
}

.dropdown-menu a:hover {
  background: var(--light-gray);
  color: var(--primary-color);
}

/* Auth Section */
.nav-auth {
  display: flex;
  align-items: center;
}

.auth-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.user-menu {
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: background var(--transition-normal);
}

.user-info:hover {
  background: var(--light-gray);
}

.user-info i {
  margin-left: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--white);
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) 0;
  min-width: 180px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-normal);
  list-style: none;
  z-index: 1001;
}

.user-menu:hover .user-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-dropdown a {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  text-decoration: none;
  color: var(--dark-gray);
  transition: all var(--transition-normal);
}

.user-dropdown a:hover {
  background: var(--light-gray);
  color: var(--primary-color);
}

.user-dropdown i {
  margin-right: var(--spacing-sm);
  width: 16px;
}

/* Mobile Navigation Toggle */
.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: var(--spacing-sm);
}

.nav-toggle .bar {
  width: 25px;
  height: 3px;
  background: var(--dark-gray);
  margin: 3px 0;
  transition: all var(--transition-normal);
  border-radius: 2px;
}

.nav-toggle.active .bar:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.nav-toggle.active .bar:nth-child(2) {
  opacity: 0;
}

.nav-toggle.active .bar:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

/* Hero Section */
.hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-top: 80px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.hero-background::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(233, 30, 99, 0.8),
    rgba(233, 30, 99, 0.6)
  );
  z-index: 1;
}

.hero-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  color: var(--white);
  max-width: 800px;
  padding: 0 var(--spacing-lg);
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-2xl);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: center;
  flex-wrap: wrap;
}

/* Features Section */
.features {
  padding: var(--spacing-2xl) 0;
  background: var(--light-gray);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
}

.feature-card {
  background: var(--white);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
  color: var(--white);
  font-size: var(--font-size-2xl);
}

.feature-card h3 {
  color: var(--dark-gray);
  margin-bottom: var(--spacing-md);
}

.feature-card p {
  color: var(--gray);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.feature-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  transition: color var(--transition-normal);
}

.feature-link:hover {
  color: var(--primary-dark);
}

/* Statistics Section */
.stats {
  padding: var(--spacing-2xl) 0;
  background: var(--gradient-primary);
  color: var(--white);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
  text-align: center;
}

.stat-item {
  padding: var(--spacing-lg);
}

.stat-number {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.stat-label {
  font-size: var(--font-size-lg);
  opacity: 0.9;
}

/* Chatbot */
.chatbot-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  border-radius: 50%;
  font-size: var(--font-size-xl);
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  z-index: 1000;
}

.chatbot-toggle:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.chatbot-container {
  position: fixed;
  bottom: 90px;
  right: 20px;
  width: 350px;
  height: 500px;
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  display: none;
  flex-direction: column;
  z-index: 1001;
  overflow: hidden;
}

.chatbot-container.active {
  display: flex;
}

.chatbot-header {
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chatbot-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
}

.chatbot-close {
  background: none;
  border: none;
  color: var(--white);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: background var(--transition-normal);
}

.chatbot-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.chatbot-messages {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.message {
  max-width: 80%;
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  word-wrap: break-word;
}

.bot-message {
  align-self: flex-start;
  background: var(--light-gray);
  color: var(--dark-gray);
}

.user-message {
  align-self: flex-end;
  background: var(--gradient-primary);
  color: var(--white);
}

.message-content {
  line-height: 1.5;
}

.chatbot-input {
  padding: var(--spacing-lg);
  border-top: 1px solid #e0e0e0;
  display: flex;
  gap: var(--spacing-sm);
}

.chatbot-input input {
  flex: 1;
  padding: var(--spacing-md);
  border: 1px solid #e0e0e0;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
}

.chatbot-input input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.chatbot-input button {
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background var(--transition-normal);
}

.chatbot-input button:hover {
  background: var(--primary-dark);
}

/* Footer */
.footer {
  background: var(--dark-gray);
  color: var(--white);
  padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.footer-section h3 {
  color: var(--white);
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-xl);
}

.footer-section h4 {
  color: var(--white);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-lg);
}

.footer-section p {
  color: #ccc;
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
  color: #ccc;
  text-decoration: none;
  transition: color var(--transition-normal);
}

.footer-section ul li a:hover {
  color: var(--primary-light);
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  color: var(--white);
  border-radius: 50%;
  text-decoration: none;
  transition: all var(--transition-normal);
}

.social-links a:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid #555;
  color: #ccc;
}
