"""
Gemini AI service for chatbot functionality in Preg and Baby Care application.
Handles AI-powered responses for pregnancy and baby care questions.
"""

import os
import logging
import json
from typing import Dict, List, Optional
import google.generativeai as genai
from datetime import datetime

logger = logging.getLogger(__name__)

class GeminiService:
    """Service class for interacting with Google Gemini AI."""
    
    def __init__(self):
        """Initialize Gemini service with API key and configuration."""
        self.api_key = os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            logger.error("GEMINI_API_KEY not found in environment variables")
            raise ValueError("Gemini API key is required")
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)
        
        # Initialize model
        self.model = genai.GenerativeModel('gemini-pro')
        
        # System prompt for pregnancy and baby care context
        self.system_prompt = """
        You are a helpful AI assistant specializing in pregnancy and baby care. 
        You provide accurate, evidence-based information about:
        - Pregnancy health and wellness
        - Prenatal nutrition and meal planning
        - Exercise during pregnancy
        - Baby care and development
        - Breastfeeding and infant nutrition
        - Sleep patterns for babies and mothers
        - Common pregnancy symptoms and concerns
        - Postpartum care
        
        Important guidelines:
        1. Always emphasize that your advice is for informational purposes only
        2. Recommend consulting healthcare providers for medical concerns
        3. Be supportive and empathetic in your responses
        4. Provide practical, actionable advice when appropriate
        5. If asked about serious medical conditions, always recommend immediate medical attention
        6. Keep responses concise but informative (under 300 words)
        7. Use a warm, caring tone appropriate for expectant and new mothers
        
        If asked about topics outside pregnancy and baby care, politely redirect to relevant topics.
        """
    
    def generate_response(self, message: str, context: Optional[Dict] = None) -> str:
        """
        Generate AI response for user message.
        
        Args:
            message: User's message/question
            context: Optional context about user (pregnancy week, baby age, etc.)
            
        Returns:
            AI-generated response string
        """
        try:
            # Prepare the full prompt with context
            full_prompt = self._prepare_prompt(message, context)
            
            # Generate response
            response = self.model.generate_content(full_prompt)
            
            if response and response.text:
                return response.text.strip()
            else:
                return "I apologize, but I'm having trouble generating a response right now. Please try again or consult with your healthcare provider."
                
        except Exception as e:
            logger.error(f"Error generating Gemini response: {str(e)}")
            return "I'm sorry, I'm experiencing technical difficulties. Please try again later or contact your healthcare provider for immediate concerns."
    
    def _prepare_prompt(self, message: str, context: Optional[Dict] = None) -> str:
        """
        Prepare the full prompt with system instructions and context.
        
        Args:
            message: User's message
            context: User context information
            
        Returns:
            Complete prompt string
        """
        prompt_parts = [self.system_prompt]
        
        # Add user context if available
        if context:
            context_info = self._format_context(context)
            if context_info:
                prompt_parts.append(f"\nUser Context:\n{context_info}")
        
        # Add user message
        prompt_parts.append(f"\nUser Question: {message}")
        prompt_parts.append("\nPlease provide a helpful, caring response:")
        
        return "\n".join(prompt_parts)
    
    def _format_context(self, context: Dict) -> str:
        """
        Format user context information for the prompt.
        
        Args:
            context: Dictionary containing user context
            
        Returns:
            Formatted context string
        """
        context_parts = []
        
        # Pregnancy information
        if context.get('is_pregnant'):
            if context.get('pregnancy_week'):
                context_parts.append(f"Currently {context['pregnancy_week']} weeks pregnant")
            if context.get('due_date'):
                context_parts.append(f"Due date: {context['due_date']}")
            if context.get('trimester'):
                context_parts.append(f"In {context['trimester']} trimester")
        
        # Baby information
        if context.get('has_baby'):
            if context.get('baby_age_months'):
                context_parts.append(f"Baby is {context['baby_age_months']} months old")
            if context.get('baby_age_weeks'):
                context_parts.append(f"Baby is {context['baby_age_weeks']} weeks old")
        
        # Health information
        if context.get('medical_conditions'):
            context_parts.append(f"Medical conditions: {context['medical_conditions']}")
        
        if context.get('allergies'):
            context_parts.append(f"Allergies: {context['allergies']}")
        
        # Previous conversation context
        if context.get('previous_topics'):
            context_parts.append(f"Previous discussion topics: {', '.join(context['previous_topics'])}")
        
        return "; ".join(context_parts) if context_parts else ""
    
    def get_quick_responses(self, category: str) -> List[str]:
        """
        Get predefined quick responses for common categories.
        
        Args:
            category: Category of quick responses (nutrition, exercise, symptoms, etc.)
            
        Returns:
            List of quick response suggestions
        """
        quick_responses = {
            'nutrition': [
                "What foods should I eat during pregnancy?",
                "How much weight should I gain during pregnancy?",
                "What foods should I avoid while pregnant?",
                "Can I drink coffee during pregnancy?",
                "What vitamins do I need during pregnancy?"
            ],
            'exercise': [
                "What exercises are safe during pregnancy?",
                "How much exercise should I do while pregnant?",
                "Can I continue my regular workout routine?",
                "What exercises should I avoid during pregnancy?",
                "When should I stop exercising during pregnancy?"
            ],
            'symptoms': [
                "Is morning sickness normal?",
                "How can I deal with pregnancy fatigue?",
                "What can I do about back pain during pregnancy?",
                "Is it normal to have mood swings?",
                "When should I call my doctor?"
            ],
            'baby_care': [
                "How often should I feed my newborn?",
                "How much sleep does a newborn need?",
                "When will my baby start sleeping through the night?",
                "How do I know if my baby is getting enough milk?",
                "What are the signs of a growth spurt?"
            ],
            'development': [
                "When will my baby start smiling?",
                "When do babies start rolling over?",
                "When should my baby start solid foods?",
                "How can I encourage my baby's development?",
                "What milestones should I watch for?"
            ]
        }
        
        return quick_responses.get(category, [])
    
    def analyze_sentiment(self, message: str) -> str:
        """
        Analyze the sentiment of user message to provide appropriate response tone.
        
        Args:
            message: User's message
            
        Returns:
            Sentiment category (positive, neutral, concerned, urgent)
        """
        # Simple keyword-based sentiment analysis
        urgent_keywords = ['emergency', 'bleeding', 'severe pain', 'can\'t breathe', 'dizzy', 'faint']
        concerned_keywords = ['worried', 'concerned', 'scared', 'anxious', 'pain', 'problem']
        positive_keywords = ['excited', 'happy', 'great', 'wonderful', 'amazing', 'love']
        
        message_lower = message.lower()
        
        if any(keyword in message_lower for keyword in urgent_keywords):
            return 'urgent'
        elif any(keyword in message_lower for keyword in concerned_keywords):
            return 'concerned'
        elif any(keyword in message_lower for keyword in positive_keywords):
            return 'positive'
        else:
            return 'neutral'
    
    def get_emergency_response(self) -> str:
        """
        Get standard emergency response for urgent situations.
        
        Returns:
            Emergency response message
        """
        return """
        ⚠️ IMPORTANT: If you're experiencing a medical emergency, please:
        
        1. Call emergency services (911) immediately
        2. Contact your healthcare provider
        3. Go to the nearest emergency room
        
        Some urgent pregnancy symptoms that require immediate attention include:
        - Severe bleeding
        - Severe abdominal pain
        - Difficulty breathing
        - Severe headaches with vision changes
        - Signs of preterm labor
        
        Please seek immediate medical care. Your health and your baby's health are the top priority.
        """

# Global instance
gemini_service = GeminiService() if os.getenv('GEMINI_API_KEY') else None
