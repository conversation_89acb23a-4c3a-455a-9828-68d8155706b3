"""
Database initialization and connection management for Preg and Baby Care application.
Uses SQLite for data storage with proper schema creation and migration support.
"""

import sqlite3
import os
import logging
from datetime import datetime
from contextlib import contextmanager

logger = logging.getLogger(__name__)

# Database file path
DB_PATH = os.path.join(os.path.dirname(__file__), '..', 'database', 'preg_baby_care.db')

def get_db_connection():
    """Get database connection with row factory."""
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

@contextmanager
def get_db():
    """Context manager for database connections."""
    conn = get_db_connection()
    try:
        yield conn
    finally:
        conn.close()

def init_db():
    """Initialize database with all required tables."""
    logger.info("Initializing database...")
    
    with get_db() as conn:
        cursor = conn.cursor()
        
        # Enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Users table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                phone TEXT,
                date_of_birth DATE,
                is_admin BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                email_verified BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # User profiles table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_profiles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                due_date DATE,
                current_week INTEGER,
                pre_pregnancy_weight REAL,
                height REAL,
                blood_type TEXT,
                allergies TEXT,
                medical_conditions TEXT,
                emergency_contact_name TEXT,
                emergency_contact_phone TEXT,
                doctor_name TEXT,
                doctor_phone TEXT,
                hospital_name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
        """)
        
        # Weight tracking table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS weight_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                weight REAL NOT NULL,
                week INTEGER NOT NULL,
                date_recorded DATE NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
        """)
        
        # Nutrition plans table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS nutrition_plans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                trimester INTEGER NOT NULL,
                calories_per_day INTEGER,
                protein_grams REAL,
                carbs_grams REAL,
                fat_grams REAL,
                fiber_grams REAL,
                iron_mg REAL,
                calcium_mg REAL,
                folic_acid_mcg REAL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Daily meal plans table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS daily_meal_plans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                day_of_week TEXT NOT NULL,
                trimester INTEGER NOT NULL,
                cuisine_type TEXT DEFAULT 'standard',
                meal_type TEXT NOT NULL,
                main_dish TEXT NOT NULL,
                sides TEXT,
                drink TEXT,
                nutrition_highlights TEXT,
                calories INTEGER,
                protein_grams REAL,
                carbs_grams REAL,
                fat_grams REAL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Exercise guides table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS exercise_guides (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                category TEXT NOT NULL,
                difficulty_level TEXT NOT NULL,
                duration_minutes INTEGER NOT NULL,
                trimester_safe TEXT NOT NULL,
                instructions TEXT NOT NULL,
                benefits TEXT,
                precautions TEXT,
                equipment_needed TEXT,
                video_url TEXT,
                image_url TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Baby care content table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS baby_care_content (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                category TEXT NOT NULL,
                age_group TEXT NOT NULL,
                tags TEXT,
                image_url TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Baby activities table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS baby_activities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT NOT NULL,
                age_group TEXT NOT NULL,
                duration_minutes INTEGER,
                materials_needed TEXT,
                instructions TEXT NOT NULL,
                benefits TEXT,
                safety_notes TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Vaccinations table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS vaccinations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                age_months INTEGER NOT NULL,
                is_required BOOLEAN DEFAULT TRUE,
                side_effects TEXT,
                contraindications TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # User vaccination records table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_vaccination_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                vaccination_id INTEGER NOT NULL,
                date_administered DATE NOT NULL,
                administered_by TEXT,
                location TEXT,
                batch_number TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                FOREIGN KEY (vaccination_id) REFERENCES vaccinations (id)
            )
        """)
        
        # Appointments table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS appointments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                doctor_name TEXT NOT NULL,
                appointment_type TEXT NOT NULL,
                appointment_date DATE NOT NULL,
                appointment_time TIME NOT NULL,
                duration_minutes INTEGER DEFAULT 30,
                location TEXT,
                notes TEXT,
                status TEXT DEFAULT 'scheduled',
                reminder_sent BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
        """)
        
        # Government schemes table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS government_schemes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT NOT NULL,
                eligibility_criteria TEXT NOT NULL,
                benefits TEXT NOT NULL,
                application_process TEXT NOT NULL,
                required_documents TEXT,
                contact_info TEXT,
                website_url TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Sleep patterns table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sleep_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT NOT NULL,
                age_group TEXT NOT NULL,
                recommended_hours INTEGER NOT NULL,
                tips TEXT NOT NULL,
                common_issues TEXT,
                solutions TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Chatbot conversations table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS chatbot_conversations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                session_id TEXT NOT NULL,
                message TEXT NOT NULL,
                response TEXT NOT NULL,
                context TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
            )
        """)
        
        # Create indexes for better performance
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_weight_entries_user_id ON weight_entries(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_appointments_user_id ON appointments(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(appointment_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_meal_plans_trimester ON daily_meal_plans(trimester)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_exercise_guides_category ON exercise_guides(category)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_baby_care_age_group ON baby_care_content(age_group)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_chatbot_session ON chatbot_conversations(session_id)")
        
        conn.commit()
        logger.info("Database initialized successfully")

def create_admin_user():
    """Create default admin user if it doesn't exist."""
    from werkzeug.security import generate_password_hash
    
    with get_db() as conn:
        cursor = conn.cursor()
        
        # Check if admin user exists
        cursor.execute("SELECT id FROM users WHERE email = ?", ('<EMAIL>',))
        if cursor.fetchone():
            return
        
        # Create admin user
        password_hash = generate_password_hash('admin123')
        cursor.execute("""
            INSERT INTO users (email, password_hash, first_name, last_name, is_admin, email_verified)
            VALUES (?, ?, ?, ?, ?, ?)
        """, ('<EMAIL>', password_hash, 'Admin', 'User', True, True))
        
        conn.commit()
        logger.info("Default admin user created")

if __name__ == "__main__":
    init_db()
    create_admin_user()
