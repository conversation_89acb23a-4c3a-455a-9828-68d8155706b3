"""
Utility helper functions for the Preg and Baby Care application.
Contains common functions for validation, error handling, and data processing.
"""

import logging
import re
from datetime import datetime, date
from flask import jsonify
from functools import wraps

logger = logging.getLogger(__name__)

def handle_error(error, status_code=500):
    """Handle and log errors consistently."""
    error_message = str(error)
    logger.error(f"Error occurred: {error_message}")
    
    # Don't expose internal errors in production
    if status_code == 500:
        error_message = "Internal server error"
    
    return jsonify({'message': error_message}), status_code

def validate_request(required_fields):
    """Decorator to validate required fields in request JSON."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            from flask import request
            
            if not request.is_json:
                return jsonify({'message': 'Request must be JSON'}), 400
            
            data = request.get_json()
            
            for field in required_fields:
                if field not in data or data[field] is None or data[field] == '':
                    return jsonify({'message': f'{field} is required'}), 400
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def validate_email(email):
    """Validate email format."""
    pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
    return re.match(pattern, email) is not None

def validate_phone(phone):
    """Validate phone number format."""
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Check if it's a valid length (10-15 digits)
    return 10 <= len(digits_only) <= 15

def validate_date(date_string):
    """Validate date string format (YYYY-MM-DD)."""
    try:
        datetime.strptime(date_string, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def validate_time(time_string):
    """Validate time string format (HH:MM)."""
    try:
        datetime.strptime(time_string, '%H:%M')
        return True
    except ValueError:
        return False

def sanitize_string(text, max_length=None):
    """Sanitize string input by removing dangerous characters."""
    if not text:
        return ''
    
    # Remove HTML tags and dangerous characters
    text = re.sub(r'<[^>]*>', '', str(text))
    text = re.sub(r'[<>"\']', '', text)
    text = text.strip()
    
    if max_length and len(text) > max_length:
        text = text[:max_length]
    
    return text

def format_date(date_obj):
    """Format date object to string."""
    if isinstance(date_obj, str):
        return date_obj
    if isinstance(date_obj, (date, datetime)):
        return date_obj.strftime('%Y-%m-%d')
    return None

def format_datetime(datetime_obj):
    """Format datetime object to string."""
    if isinstance(datetime_obj, str):
        return datetime_obj
    if isinstance(datetime_obj, datetime):
        return datetime_obj.strftime('%Y-%m-%d %H:%M:%S')
    return None

def calculate_pregnancy_week(due_date):
    """Calculate current pregnancy week based on due date."""
    if not due_date:
        return None
    
    try:
        if isinstance(due_date, str):
            due_date = datetime.strptime(due_date, '%Y-%m-%d').date()
        
        # Pregnancy is typically 40 weeks
        conception_date = due_date - timedelta(weeks=40)
        current_date = date.today()
        
        if current_date < conception_date:
            return 0
        
        weeks_pregnant = (current_date - conception_date).days // 7
        return min(weeks_pregnant, 42)  # Cap at 42 weeks
        
    except (ValueError, TypeError):
        return None

def calculate_bmi(weight_kg, height_cm):
    """Calculate BMI from weight and height."""
    try:
        height_m = height_cm / 100
        bmi = weight_kg / (height_m ** 2)
        return round(bmi, 1)
    except (ValueError, TypeError, ZeroDivisionError):
        return None

def get_bmi_category(bmi):
    """Get BMI category based on BMI value."""
    if bmi is None:
        return None
    
    if bmi < 18.5:
        return 'Underweight'
    elif bmi < 25:
        return 'Normal weight'
    elif bmi < 30:
        return 'Overweight'
    else:
        return 'Obese'

def calculate_recommended_weight_gain(pre_pregnancy_bmi):
    """Calculate recommended weight gain based on pre-pregnancy BMI."""
    if pre_pregnancy_bmi is None:
        return None
    
    if pre_pregnancy_bmi < 18.5:
        return {'min': 12.5, 'max': 18}  # Underweight
    elif pre_pregnancy_bmi < 25:
        return {'min': 11.5, 'max': 16}  # Normal weight
    elif pre_pregnancy_bmi < 30:
        return {'min': 7, 'max': 11.5}   # Overweight
    else:
        return {'min': 5, 'max': 9}      # Obese

def paginate_results(query_results, page=1, per_page=10):
    """Paginate query results."""
    try:
        page = max(1, int(page))
        per_page = max(1, min(100, int(per_page)))  # Cap at 100 items per page
        
        total = len(query_results)
        start = (page - 1) * per_page
        end = start + per_page
        
        items = query_results[start:end]
        
        return {
            'items': items,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page,
            'has_prev': page > 1,
            'has_next': end < total
        }
    except (ValueError, TypeError):
        return {
            'items': [],
            'total': 0,
            'page': 1,
            'per_page': per_page,
            'pages': 0,
            'has_prev': False,
            'has_next': False
        }

def generate_time_slots(start_time='09:00', end_time='17:00', interval_minutes=30):
    """Generate available time slots for appointments."""
    slots = []
    
    try:
        start = datetime.strptime(start_time, '%H:%M')
        end = datetime.strptime(end_time, '%H:%M')
        
        current = start
        while current < end:
            slots.append(current.strftime('%H:%M'))
            current += timedelta(minutes=interval_minutes)
        
        return slots
    except ValueError:
        return []

def is_admin_user(user_id):
    """Check if user is an admin."""
    from models.database import get_db
    
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT is_admin FROM users WHERE id = ?", (user_id,))
            user = cursor.fetchone()
            return user and user['is_admin']
    except Exception:
        return False

def require_admin(f):
    """Decorator to require admin privileges."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        from flask_jwt_extended import get_jwt_identity
        
        user_id = get_jwt_identity()
        if not is_admin_user(user_id):
            return jsonify({'message': 'Admin privileges required'}), 403
        
        return f(*args, **kwargs)
    return decorated_function

def log_user_activity(user_id, action, details=None):
    """Log user activity for audit purposes."""
    try:
        logger.info(f"User {user_id} performed action: {action}")
        if details:
            logger.info(f"Details: {details}")
    except Exception as e:
        logger.error(f"Failed to log user activity: {e}")

def clean_dict(data):
    """Remove None values and empty strings from dictionary."""
    if not isinstance(data, dict):
        return data
    
    return {k: v for k, v in data.items() if v is not None and v != ''}

def convert_to_dict(row):
    """Convert SQLite Row object to dictionary."""
    if row is None:
        return None
    return dict(row)

def convert_rows_to_dicts(rows):
    """Convert list of SQLite Row objects to list of dictionaries."""
    return [dict(row) for row in rows] if rows else []

from datetime import timedelta
