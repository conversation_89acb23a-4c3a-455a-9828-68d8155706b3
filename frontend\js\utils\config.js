// Application Configuration
const CONFIG = {
    // API Configuration
    API: {
        BASE_URL: 'http://localhost:5000/api',
        TIMEOUT: 10000,
        RETRY_ATTEMPTS: 3
    },
    
    // Authentication
    AUTH: {
        TOKEN_KEY: 'preg_baby_care_token',
        USER_KEY: 'preg_baby_care_user',
        TOKEN_EXPIRY_HOURS: 24
    },
    
    // Gemini AI Configuration
    GEMINI: {
        API_KEY: 'AIzaSyCowVCTXr09wsVZSW-Uv3vO8p69dzsp6Wo',
        MODEL: 'gemini-pro',
        MAX_TOKENS: 1000
    },
    
    // Application Settings
    APP: {
        NAME: 'Preg and Baby Care',
        VERSION: '1.0.0',
        DESCRIPTION: 'Comprehensive pregnancy and baby care guidance',
        AUTHOR: 'Preg and Baby Care Team'
    },
    
    // UI Settings
    UI: {
        ANIMATION_DURATION: 300,
        DEBOUNCE_DELAY: 500,
        TOAST_DURATION: 5000,
        LOADING_MIN_DURATION: 1000
    },
    
    // Validation Rules
    VALIDATION: {
        EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        PASSWORD_MIN_LENGTH: 8,
        NAME_MIN_LENGTH: 2,
        PHONE_REGEX: /^[\+]?[1-9][\d]{0,15}$/
    },
    
    // Chart Configuration
    CHARTS: {
        COLORS: {
            PRIMARY: '#e91e63',
            SECONDARY: '#2196f3',
            SUCCESS: '#4caf50',
            WARNING: '#ff9800',
            ERROR: '#f44336',
            GRADIENT: ['#e91e63', '#ad1457']
        },
        WEIGHT_TRACKER: {
            MIN_WEIGHT: 30,
            MAX_WEIGHT: 150,
            WEEKS: 40
        }
    },
    
    // Nutrition Plans
    NUTRITION: {
        TRIMESTERS: [
            { id: 1, name: 'First Trimester', weeks: '1-12' },
            { id: 2, name: 'Second Trimester', weeks: '13-27' },
            { id: 3, name: 'Third Trimester', weeks: '28-40' }
        ],
        MEAL_TYPES: ['breakfast', 'lunch', 'dinner', 'snacks'],
        CUISINE_TYPES: ['standard', 'vegetarian', 'vegan', 'gluten-free']
    },
    
    // Exercise Categories
    EXERCISE: {
        CATEGORIES: [
            'Cardio',
            'Strength Training',
            'Flexibility',
            'Prenatal Yoga',
            'Breathing Exercises',
            'Pelvic Floor'
        ],
        DIFFICULTY_LEVELS: ['Beginner', 'Intermediate', 'Advanced'],
        DURATION_OPTIONS: [10, 15, 20, 30, 45, 60]
    },
    
    // Baby Care
    BABY_CARE: {
        AGE_GROUPS: [
            { id: 'newborn', name: 'Newborn (0-2 months)', min: 0, max: 2 },
            { id: 'infant', name: 'Infant (2-12 months)', min: 2, max: 12 },
            { id: 'toddler', name: 'Toddler (1-3 years)', min: 12, max: 36 }
        ],
        FEEDING_TYPES: ['breastfeeding', 'formula', 'mixed', 'solid_foods'],
        SLEEP_PATTERNS: {
            NEWBORN: { total: 16, naps: 4 },
            INFANT: { total: 14, naps: 3 },
            TODDLER: { total: 12, naps: 1 }
        }
    },
    
    // Appointment Types
    APPOINTMENTS: {
        TYPES: [
            'Regular Checkup',
            'Ultrasound',
            'Blood Test',
            'Consultation',
            'Emergency',
            'Vaccination'
        ],
        DURATIONS: [15, 30, 45, 60, 90, 120],
        TIME_SLOTS: [
            '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
            '12:00', '12:30', '14:00', '14:30', '15:00', '15:30',
            '16:00', '16:30', '17:00', '17:30'
        ]
    },
    
    // Local Storage Keys
    STORAGE: {
        THEME: 'preg_baby_care_theme',
        LANGUAGE: 'preg_baby_care_language',
        PREFERENCES: 'preg_baby_care_preferences',
        CACHE: 'preg_baby_care_cache'
    },
    
    // Error Messages
    ERRORS: {
        NETWORK: 'Network error. Please check your connection.',
        UNAUTHORIZED: 'Please log in to continue.',
        FORBIDDEN: 'You do not have permission to perform this action.',
        NOT_FOUND: 'The requested resource was not found.',
        SERVER_ERROR: 'Server error. Please try again later.',
        VALIDATION: 'Please check your input and try again.',
        TIMEOUT: 'Request timed out. Please try again.'
    },
    
    // Success Messages
    SUCCESS: {
        LOGIN: 'Successfully logged in!',
        LOGOUT: 'Successfully logged out!',
        REGISTER: 'Account created successfully!',
        UPDATE: 'Information updated successfully!',
        DELETE: 'Item deleted successfully!',
        SAVE: 'Changes saved successfully!',
        APPOINTMENT_BOOKED: 'Appointment booked successfully!'
    },
    
    // Pagination
    PAGINATION: {
        DEFAULT_PAGE_SIZE: 10,
        PAGE_SIZE_OPTIONS: [5, 10, 20, 50],
        MAX_VISIBLE_PAGES: 5
    },
    
    // File Upload
    UPLOAD: {
        MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
        ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
        ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
    },
    
    // Social Media Links
    SOCIAL: {
        FACEBOOK: 'https://facebook.com/pregbabycare',
        TWITTER: 'https://twitter.com/pregbabycare',
        INSTAGRAM: 'https://instagram.com/pregbabycare',
        YOUTUBE: 'https://youtube.com/pregbabycare'
    },
    
    // Contact Information
    CONTACT: {
        EMAIL: '<EMAIL>',
        PHONE: '******-PREG-CARE',
        ADDRESS: '123 Healthcare Ave, Medical City, MC 12345',
        SUPPORT_HOURS: 'Monday - Friday: 9:00 AM - 6:00 PM'
    }
};

// Environment-specific overrides
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    CONFIG.API.BASE_URL = 'http://localhost:5000/api';
} else {
    CONFIG.API.BASE_URL = 'https://api.pregbabycare.com/api';
}

// Freeze configuration to prevent modifications
Object.freeze(CONFIG);

// Export for use in other modules
window.CONFIG = CONFIG;
