/**
 * Authentication utility functions for Preg and Baby Care application.
 * Handles user authentication, token management, and user session.
 */

class AuthManager {
    constructor() {
        this.tokenKey = CONFIG.AUTH.TOKEN_KEY;
        this.userKey = CONFIG.AUTH.USER_KEY;
        this.currentUser = null;
        this.authCallbacks = [];
        
        // Initialize authentication state
        this.init();
    }

    /**
     * Initialize authentication manager
     */
    init() {
        this.loadUserFromStorage();
        this.setupTokenRefresh();
        this.updateUI();
    }

    /**
     * Load user data from localStorage
     */
    loadUserFromStorage() {
        try {
            const token = localStorage.getItem(this.tokenKey);
            const userData = localStorage.getItem(this.userKey);
            
            if (token && userData) {
                this.currentUser = JSON.parse(userData);
                this.currentUser.token = token;
            }
        } catch (error) {
            console.error('Error loading user from storage:', error);
            this.logout();
        }
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!this.getToken();
    }

    /**
     * Check if current user is admin
     */
    isAdmin() {
        return this.currentUser && this.currentUser.is_admin;
    }

    /**
     * Get current authentication token
     */
    getToken() {
        return localStorage.getItem(this.tokenKey);
    }

    /**
     * Get current user data
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * Login user with credentials
     */
    async login(credentials) {
        try {
            const response = await API.auth.login(credentials);
            
            // Store authentication data
            localStorage.setItem(this.tokenKey, response.access_token);
            localStorage.setItem(this.userKey, JSON.stringify(response.user));
            
            // Update current user
            this.currentUser = response.user;
            this.currentUser.token = response.access_token;
            
            // Update UI and notify callbacks
            this.updateUI();
            this.notifyAuthCallbacks('login', this.currentUser);
            
            return response;
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }

    /**
     * Register new user
     */
    async register(userData) {
        try {
            const response = await API.auth.register(userData);
            
            // Store authentication data
            localStorage.setItem(this.tokenKey, response.access_token);
            localStorage.setItem(this.userKey, JSON.stringify(response.user));
            
            // Update current user
            this.currentUser = response.user;
            this.currentUser.token = response.access_token;
            
            // Update UI and notify callbacks
            this.updateUI();
            this.notifyAuthCallbacks('register', this.currentUser);
            
            return response;
        } catch (error) {
            console.error('Registration error:', error);
            throw error;
        }
    }

    /**
     * Logout user
     */
    async logout() {
        try {
            // Call logout API if user is authenticated
            if (this.isAuthenticated()) {
                await API.auth.logout().catch(() => {
                    // Ignore logout API errors
                });
            }
        } catch (error) {
            console.error('Logout API error:', error);
        } finally {
            // Clear local storage
            localStorage.removeItem(this.tokenKey);
            localStorage.removeItem(this.userKey);
            
            // Clear current user
            this.currentUser = null;
            
            // Update UI and notify callbacks
            this.updateUI();
            this.notifyAuthCallbacks('logout', null);
        }
    }

    /**
     * Update user profile
     */
    async updateProfile(profileData) {
        try {
            const response = await API.user.updateProfile(profileData);
            
            // Update current user data
            if (this.currentUser) {
                Object.assign(this.currentUser, profileData);
                localStorage.setItem(this.userKey, JSON.stringify(this.currentUser));
            }
            
            this.notifyAuthCallbacks('profileUpdate', this.currentUser);
            
            return response;
        } catch (error) {
            console.error('Profile update error:', error);
            throw error;
        }
    }

    /**
     * Change user password
     */
    async changePassword(passwordData) {
        try {
            const response = await API.user.changePassword(passwordData);
            this.notifyAuthCallbacks('passwordChange', this.currentUser);
            return response;
        } catch (error) {
            console.error('Password change error:', error);
            throw error;
        }
    }

    /**
     * Setup automatic token refresh
     */
    setupTokenRefresh() {
        // Check token validity every 30 minutes
        setInterval(() => {
            if (this.isAuthenticated()) {
                this.validateToken();
            }
        }, 30 * 60 * 1000);
    }

    /**
     * Validate current token
     */
    async validateToken() {
        try {
            await API.user.getProfile();
        } catch (error) {
            if (error.status === 401) {
                console.log('Token expired, logging out...');
                this.logout();
            }
        }
    }

    /**
     * Update UI based on authentication state
     */
    updateUI() {
        const authButtons = document.getElementById('auth-buttons');
        const userMenu = document.getElementById('user-menu');
        const userName = document.getElementById('user-name');
        const adminLink = document.getElementById('admin-link');

        if (!authButtons || !userMenu) return;

        if (this.isAuthenticated()) {
            // Show user menu, hide auth buttons
            authButtons.style.display = 'none';
            userMenu.style.display = 'block';
            
            // Update user name
            if (userName && this.currentUser) {
                userName.textContent = `${this.currentUser.first_name} ${this.currentUser.last_name}`;
            }
            
            // Show/hide admin link
            if (adminLink) {
                adminLink.style.display = this.isAdmin() ? 'block' : 'none';
            }
        } else {
            // Show auth buttons, hide user menu
            authButtons.style.display = 'flex';
            userMenu.style.display = 'none';
        }
    }

    /**
     * Add authentication callback
     */
    onAuthChange(callback) {
        this.authCallbacks.push(callback);
    }

    /**
     * Remove authentication callback
     */
    offAuthChange(callback) {
        const index = this.authCallbacks.indexOf(callback);
        if (index > -1) {
            this.authCallbacks.splice(index, 1);
        }
    }

    /**
     * Notify authentication callbacks
     */
    notifyAuthCallbacks(event, user) {
        this.authCallbacks.forEach(callback => {
            try {
                callback(event, user);
            } catch (error) {
                console.error('Auth callback error:', error);
            }
        });
    }

    /**
     * Require authentication for page access
     */
    requireAuth(redirectUrl = '/pages/login.html') {
        if (!this.isAuthenticated()) {
            window.location.href = redirectUrl;
            return false;
        }
        return true;
    }

    /**
     * Require admin privileges
     */
    requireAdmin(redirectUrl = '/index.html') {
        if (!this.isAuthenticated() || !this.isAdmin()) {
            window.location.href = redirectUrl;
            return false;
        }
        return true;
    }

    /**
     * Get user initials for avatar
     */
    getUserInitials() {
        if (!this.currentUser) return '';
        
        const firstName = this.currentUser.first_name || '';
        const lastName = this.currentUser.last_name || '';
        
        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
    }

    /**
     * Format user display name
     */
    getUserDisplayName() {
        if (!this.currentUser) return '';
        
        return `${this.currentUser.first_name} ${this.currentUser.last_name}`.trim();
    }
}

// Create global auth manager instance
const auth = new AuthManager();

// Setup logout button handler
document.addEventListener('DOMContentLoaded', function() {
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            auth.logout().then(() => {
                window.location.href = '/index.html';
            });
        });
    }
});

// Export auth manager
window.auth = auth;
