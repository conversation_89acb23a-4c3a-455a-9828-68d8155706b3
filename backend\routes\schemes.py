"""
Government schemes routes for managing benefit programs and applications.
"""

import logging
from flask import Blueprint, request, jsonify

from models.database import get_db
from utils.helpers import handle_error, convert_rows_to_dicts

logger = logging.getLogger(__name__)
schemes_bp = Blueprint('schemes', __name__)

@schemes_bp.route('', methods=['GET'])
def get_schemes():
    """Get all government schemes."""
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM government_schemes
                WHERE is_active = 1
                ORDER BY name
            """)
            
            schemes = convert_rows_to_dicts(cursor.fetchall())
            
            return jsonify({'schemes': schemes}), 200
            
    except Exception as e:
        logger.error(f"Get schemes error: {str(e)}")
        return handle_error(e)

@schemes_bp.route('/<int:scheme_id>', methods=['GET'])
def get_scheme(scheme_id):
    """Get specific scheme details."""
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM government_schemes
                WHERE id = ? AND is_active = 1
            """, (scheme_id,))
            
            scheme = cursor.fetchone()
            
            if not scheme:
                return jsonify({'message': 'Scheme not found'}), 404
            
            return jsonify({'scheme': dict(scheme)}), 200
            
    except Exception as e:
        logger.error(f"Get scheme error: {str(e)}")
        return handle_error(e)
