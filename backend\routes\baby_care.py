"""
Baby care routes for baby development, activities, and care information.
"""

import logging
from flask import Blueprint, request, jsonify

from models.database import get_db
from utils.helpers import handle_error, convert_rows_to_dicts

logger = logging.getLogger(__name__)
baby_care_bp = Blueprint('baby_care', __name__)

@baby_care_bp.route('/content', methods=['GET'])
def get_baby_care_content():
    """Get baby care content with filtering."""
    try:
        category = request.args.get('category')
        age_group = request.args.get('age_group')
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM baby_care_content WHERE is_active = 1"
            params = []
            
            if category:
                query += " AND category = ?"
                params.append(category)
            
            if age_group:
                query += " AND age_group = ?"
                params.append(age_group)
            
            query += " ORDER BY age_group, category"
            
            cursor.execute(query, params)
            content = convert_rows_to_dicts(cursor.fetchall())
            
            return jsonify({'content': content}), 200
            
    except Exception as e:
        logger.error(f"Get baby care content error: {str(e)}")
        return handle_error(e)

@baby_care_bp.route('/activities', methods=['GET'])
def get_baby_activities():
    """Get baby activities."""
    try:
        age_group = request.args.get('age_group')
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM baby_activities WHERE is_active = 1"
            params = []
            
            if age_group:
                query += " AND age_group = ?"
                params.append(age_group)
            
            query += " ORDER BY age_group"
            
            cursor.execute(query, params)
            activities = convert_rows_to_dicts(cursor.fetchall())
            
            return jsonify({'activities': activities}), 200
            
    except Exception as e:
        logger.error(f"Get baby activities error: {str(e)}")
        return handle_error(e)
