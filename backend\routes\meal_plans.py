"""
Meal plans routes for daily meal planning and nutrition.
"""

import logging
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity

from models.database import get_db
from utils.helpers import handle_error, convert_rows_to_dicts, require_admin

logger = logging.getLogger(__name__)
meal_plans_bp = Blueprint('meal_plans', __name__)

@meal_plans_bp.route('', methods=['GET'])
def get_meal_plans():
    """Get meal plans with filtering options."""
    try:
        day_of_week = request.args.get('day_of_week')
        trimester = request.args.get('trimester', type=int)
        cuisine_type = request.args.get('cuisine_type', 'standard')
        meal_type = request.args.get('meal_type')
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM daily_meal_plans WHERE is_active = 1"
            params = []
            
            if day_of_week:
                query += " AND day_of_week = ?"
                params.append(day_of_week)
            
            if trimester:
                query += " AND trimester = ?"
                params.append(trimester)
            
            if cuisine_type:
                query += " AND cuisine_type = ?"
                params.append(cuisine_type)
            
            if meal_type:
                query += " AND meal_type = ?"
                params.append(meal_type)
            
            query += " ORDER BY day_of_week, meal_type"
            
            cursor.execute(query, params)
            meal_plans = convert_rows_to_dicts(cursor.fetchall())
            
            return jsonify({'meal_plans': meal_plans}), 200
            
    except Exception as e:
        logger.error(f"Get meal plans error: {str(e)}")
        return handle_error(e)

@meal_plans_bp.route('/day', methods=['GET'])
def get_day_meal_plan():
    """Get complete meal plan for a specific day."""
    try:
        day = request.args.get('day', 'monday')
        trimester = request.args.get('trimester', 1, type=int)
        cuisine = request.args.get('cuisine', 'standard')
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM daily_meal_plans
                WHERE day_of_week = ? AND trimester = ? AND cuisine_type = ? AND is_active = 1
                ORDER BY 
                    CASE meal_type
                        WHEN 'breakfast' THEN 1
                        WHEN 'lunch' THEN 2
                        WHEN 'dinner' THEN 3
                        WHEN 'snacks' THEN 4
                        ELSE 5
                    END
            """, (day.lower(), trimester, cuisine))
            
            meals = convert_rows_to_dicts(cursor.fetchall())
            
            # Organize meals by type
            meal_plan = {
                'day': day,
                'trimester': trimester,
                'cuisine_type': cuisine,
                'meals': {
                    'breakfast': None,
                    'lunch': None,
                    'dinner': None,
                    'snacks': []
                }
            }
            
            for meal in meals:
                if meal['meal_type'] == 'snacks':
                    meal_plan['meals']['snacks'].append(meal)
                else:
                    meal_plan['meals'][meal['meal_type']] = meal
            
            return jsonify({'meal_plan': meal_plan}), 200
            
    except Exception as e:
        logger.error(f"Get day meal plan error: {str(e)}")
        return handle_error(e)

@meal_plans_bp.route('', methods=['POST'])
@jwt_required()
@require_admin
def create_meal_plan():
    """Create new meal plan (admin only)."""
    try:
        data = request.get_json()
        
        required_fields = ['day_of_week', 'trimester', 'meal_type', 'main_dish']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'message': f'{field} is required'}), 400
        
        with get_db() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO daily_meal_plans (
                    day_of_week, trimester, cuisine_type, meal_type,
                    main_dish, sides, drink, nutrition_highlights,
                    calories, protein_grams, carbs_grams, fat_grams
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['day_of_week'].lower(),
                data['trimester'],
                data.get('cuisine_type', 'standard'),
                data['meal_type'],
                data['main_dish'],
                data.get('sides'),
                data.get('drink'),
                data.get('nutrition_highlights'),
                data.get('calories'),
                data.get('protein_grams'),
                data.get('carbs_grams'),
                data.get('fat_grams')
            ))
            
            meal_plan_id = cursor.lastrowid
            conn.commit()
            
            logger.info(f"Meal plan created: {meal_plan_id}")
            
            return jsonify({
                'message': 'Meal plan created successfully',
                'meal_plan_id': meal_plan_id
            }), 201
            
    except Exception as e:
        logger.error(f"Create meal plan error: {str(e)}")
        return handle_error(e)
